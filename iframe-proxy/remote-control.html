<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - Remote Control</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; overflow: hidden; }
        
        .header {
            background: #1a365d; color: white; padding: 10px 20px;
            display: flex; justify-content: space-between; align-items: center;
            position: fixed; top: 0; left: 0; right: 0; z-index: 1000; height: 50px;
        }
        
        .header h1 { font-size: 16px; margin: 0; }
        
        .status { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
        .status.connected { background: #10b981; }
        .status.disconnected { background: #ef4444; }
        
        .controls {
            display: flex; gap: 5px; align-items: center;
        }
        
        .btn {
            background: #4f46e5; color: white; border: none; 
            padding: 6px 12px; border-radius: 4px; cursor: pointer; 
            font-size: 12px;
        }
        .btn:hover { background: #4338ca; }
        
        .url-display {
            font-size: 11px; color: #cbd5e0; max-width: 300px; 
            overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
        }
        
        .browser-container { 
            position: fixed; top: 50px; left: 0; right: 0; bottom: 0;
            background: white; overflow: hidden;
        }
        
        .browser-content {
            width: 100%; height: 100%; overflow: auto;
            cursor: crosshair;
        }
        
        .loading { 
            display: flex; justify-content: center; align-items: center; 
            height: 100%; font-size: 18px; color: #666; 
        }
        
        .click-indicator {
            position: absolute;
            width: 20px; height: 20px;
            border: 2px solid #ef4444;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999;
            animation: clickPulse 0.5s ease-out;
        }
        
        @keyframes clickPulse {
            0% { transform: scale(0.5); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>🎮 DAT Load Board - Remote Control</h1>
            <div class="url-display" id="url-display">Connecting...</div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="refreshBrowser()">🔄 Refresh</button>
            <button class="btn" onclick="goBack()">⬅️ Back</button>
            <button class="btn" onclick="goForward()">➡️ Forward</button>
            <button class="btn" onclick="getContent()">📄 Update</button>
        </div>
        
        <div class="status disconnected" id="status">🔴 Connecting...</div>
    </div>
    
    <div class="browser-container">
        <div class="loading" id="loading">Connecting to master browser...</div>
        <div id="browser-content" class="browser-content" style="display: none;"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let lastClickTime = 0;
        
        function connect() {
            // Connect directly to the master browser's WebSocket on port 3005
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//*************:3005/`;
            
            console.log('🔌 Connecting to master browser WebSocket:', wsUrl);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                console.log('🎮 Connected to REAL master browser!');
                isConnected = true;
                updateStatus('connected', '🟢 LIVE CONTROL');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('browser-content').style.display = 'block';
                
                // Request initial content
                sendCommand('getContent');
            };
            
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log('📨 Received from master browser:', data.type);
                
                if (data.type === 'update') {
                    updateBrowserContent(data.html);
                    updateURL(data.url);
                } else if (data.type === 'error') {
                    console.error('❌ Master browser error:', data.message);
                    alert('Error: ' + data.message);
                }
            };
            
            ws.onclose = () => {
                console.log('🔌 Disconnected from master browser');
                isConnected = false;
                updateStatus('disconnected', '🔴 Disconnected');
                
                // Try to reconnect after 3 seconds
                setTimeout(connect, 3000);
            };
            
            ws.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
            };
        }
        
        function updateStatus(status, text) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = text;
        }
        
        function updateURL(url) {
            document.getElementById('url-display').textContent = url;
        }
        
        function updateBrowserContent(html) {
            const contentEl = document.getElementById('browser-content');
            
            // Inject base tag to fix relative URLs and prevent navigation
            const modifiedHtml = html.replace(
                '<head>',
                `<head>
                <base href="https://one.dat.com/">
                <style>
                    * { cursor: crosshair !important; }
                    a { pointer-events: none !important; }
                </style>`
            );
            
            contentEl.innerHTML = modifiedHtml;
            
            // Add click event listener to the entire content
            addClickListener(contentEl);
            
            // Disable all links and forms to prevent navigation
            disableNavigation(contentEl);
        }
        
        function addClickListener(container) {
            container.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Prevent double clicks
                const now = Date.now();
                if (now - lastClickTime < 500) {
                    return;
                }
                lastClickTime = now;
                
                if (isConnected) {
                    const rect = container.getBoundingClientRect();
                    const x = e.clientX - rect.left + container.scrollLeft;
                    const y = e.clientY - rect.top + container.scrollTop;
                    
                    console.log(`🖱️ Sending REAL click to master browser at (${x}, ${y})`);
                    
                    // Show click indicator
                    showClickIndicator(e.clientX, e.clientY);
                    
                    // Send REAL click to master browser
                    sendCommand('click', { x, y });
                }
            });
            
            // Handle scroll
            container.addEventListener('wheel', (e) => {
                e.preventDefault();
                
                if (isConnected) {
                    console.log(`📜 Sending REAL scroll to master browser: ${e.deltaY}`);
                    sendCommand('scroll', { deltaY: e.deltaY });
                }
            });
        }
        
        function disableNavigation(container) {
            // Disable all links
            const links = container.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                });
                link.style.pointerEvents = 'none';
            });
            
            // Disable form submissions
            const forms = container.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                });
            });
            
            // Handle input fields
            const inputs = container.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('focus', (e) => {
                    e.preventDefault();
                    
                    // Get input position and send click to master browser
                    const rect = input.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    const x = rect.left - containerRect.left + rect.width / 2 + container.scrollLeft;
                    const y = rect.top - containerRect.top + rect.height / 2 + container.scrollTop;
                    
                    console.log(`📝 Sending REAL input focus to master browser at (${x}, ${y})`);
                    sendCommand('click', { x, y });
                });
                
                input.addEventListener('input', (e) => {
                    if (isConnected && e.target.value) {
                        console.log(`⌨️ Sending REAL typing to master browser: "${e.target.value}"`);
                        sendCommand('type', { text: e.target.value });
                    }
                });
            });
        }
        
        function showClickIndicator(x, y) {
            const indicator = document.createElement('div');
            indicator.className = 'click-indicator';
            indicator.style.left = (x - 10) + 'px';
            indicator.style.top = (y - 10) + 'px';
            
            document.body.appendChild(indicator);
            
            setTimeout(() => {
                document.body.removeChild(indicator);
            }, 500);
        }
        
        function sendCommand(type, data = {}) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const command = { type, ...data };
                console.log('📤 Sending command to master browser:', command);
                ws.send(JSON.stringify(command));
            } else {
                console.warn('⚠️ WebSocket not connected');
            }
        }
        
        function refreshBrowser() {
            console.log('🔄 Sending REAL refresh to master browser');
            sendCommand('refresh');
        }
        
        function goBack() {
            console.log('⬅️ Sending REAL back to master browser');
            sendCommand('key', { key: 'Alt+ArrowLeft' });
        }
        
        function goForward() {
            console.log('➡️ Sending REAL forward to master browser');
            sendCommand('key', { key: 'Alt+ArrowRight' });
        }
        
        function getContent() {
            console.log('📄 Requesting current content from master browser');
            sendCommand('getContent');
        }
        
        // Start connection
        connect();
        
        // Auto-refresh content every 30 seconds to stay in sync
        setInterval(() => {
            if (isConnected) {
                sendCommand('getContent');
            }
        }, 30000);
        
        console.log('🎮 Remote Control initialized - ready to control master browser!');
    </script>
</body>
</html>
