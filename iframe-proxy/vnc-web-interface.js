const express = require('express');
const http = require('http');

class VNCWebInterface {
    constructor() {
        this.app = express();
        this.server = null;
    }

    setupRoutes() {
        // Main VNC interface
        this.app.get('/', (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - Remote Desktop (VNC)</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%); 
            color: white;
            overflow: hidden;
            height: 100vh;
        }
        .header {
            background: rgba(45, 55, 72, 0.95); 
            backdrop-filter: blur(10px);
            color: white; 
            padding: 15px 25px;
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            position: fixed; 
            top: 0; 
            left: 0; 
            right: 0; 
            z-index: 1000;
            height: 60px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.3);
        }
        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
        }
        .logo::before {
            content: '🚛';
            margin-right: 10px;
            font-size: 24px;
        }
        .controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .connected { 
            background: linear-gradient(45deg, #48bb78, #38a169); 
            box-shadow: 0 0 20px rgba(72, 187, 120, 0.3);
        }
        .btn {
            background: linear-gradient(45deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
        }
        .btn-success {
            background: linear-gradient(45deg, #48bb78, #38a169);
        }
        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }
        .vnc-container {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
            border-top: 3px solid #4299e1;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #a0aec0;
        }
        .loading::before {
            content: '⏳';
            display: block;
            font-size: 48px;
            margin-bottom: 20px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(45, 55, 72, 0.9);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.5;
            backdrop-filter: blur(10px);
        }
        .instructions h3 {
            color: #4299e1;
            margin-bottom: 10px;
        }
        .instructions ul {
            list-style: none;
            padding: 0;
        }
        .instructions li {
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .instructions li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #48bb78;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">DAT Load Board - Remote Desktop</div>
        <div class="controls">
            <a href="http://*************:6080/vnc.html?autoconnect=true&resize=scale" class="btn btn-success" target="_blank">
                🖥️ Full VNC Interface
            </a>
            <button class="btn" onclick="refreshVNC()">🔄 Refresh</button>
            <button class="btn" onclick="openFullscreen()">⛶ Fullscreen</button>
            <div class="status connected">🟢 LIVE</div>
        </div>
    </div>
    
    <div class="vnc-container" id="vnc-container">
        <div class="loading" id="loading">
            Loading Remote Desktop...
            <br><small>Connecting to DAT browser session</small>
        </div>
        <iframe 
            id="vnc-frame" 
            src="http://*************:6080/vnc.html?autoconnect=true&resize=scale&show_dot=true"
            onload="hideLoading()"
            style="display: none;"
        ></iframe>
    </div>

    <div class="instructions">
        <h3>🎮 How to Use Remote Desktop:</h3>
        <ul>
            <li><strong>Mouse:</strong> Click and drag normally - works exactly like a local browser</li>
            <li><strong>Keyboard:</strong> Type directly - all keys work including shortcuts</li>
            <li><strong>DAT Navigation:</strong> Use the browser normally - click Search Loads, enter criteria, etc.</li>
            <li><strong>Fullscreen:</strong> Click the fullscreen button for better experience</li>
            <li><strong>Performance:</strong> This is a live remote desktop - you're controlling the actual server browser</li>
        </ul>
    </div>

    <script>
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('vnc-frame').style.display = 'block';
        }
        
        function refreshVNC() {
            const frame = document.getElementById('vnc-frame');
            const loading = document.getElementById('loading');
            
            loading.style.display = 'block';
            frame.style.display = 'none';
            
            frame.src = frame.src;
        }
        
        function openFullscreen() {
            const container = document.getElementById('vnc-container');
            if (container.requestFullscreen) {
                container.requestFullscreen();
            } else if (container.webkitRequestFullscreen) {
                container.webkitRequestFullscreen();
            } else if (container.msRequestFullscreen) {
                container.msRequestFullscreen();
            }
        }
        
        // Auto-hide instructions after 10 seconds
        setTimeout(() => {
            const instructions = document.querySelector('.instructions');
            if (instructions) {
                instructions.style.opacity = '0.3';
                instructions.style.transition = 'opacity 1s ease';
            }
        }, 10000);
        
        // Show instructions on hover
        document.querySelector('.instructions').addEventListener('mouseenter', function() {
            this.style.opacity = '1';
        });
        
        document.querySelector('.instructions').addEventListener('mouseleave', function() {
            this.style.opacity = '0.3';
        });
    </script>
</body>
</html>`;
            res.send(html);
        });

        // Status endpoint
        this.app.get('/status', (req, res) => {
            res.json({
                status: 'running',
                vncUrl: 'http://*************:6080/vnc.html',
                directUrl: 'http://*************:6080/vnc.html?autoconnect=true&resize=scale'
            });
        });
    }

    start(port = 3009) {
        this.setupRoutes();
        
        this.server = http.createServer(this.app);
        this.server.listen(port, '0.0.0.0', () => {
            console.log(`🌐 VNC Web Interface running on port ${port}`);
            console.log(`🖥️ Remote Desktop Interface: http://*************:${port}/`);
            console.log(`📊 Status API: http://*************:${port}/status`);
        });
    }
}

// Start the web interface
const webInterface = new VNCWebInterface();
webInterface.start();

module.exports = VNCWebInterface;
