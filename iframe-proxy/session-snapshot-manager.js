const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class SessionSnapshotManager {
    constructor() {
        this.masterBrowser = null;
        this.masterPage = null;
        this.sessionSnapshots = new Map(); // Store session snapshots
        this.activeClones = new Map(); // Track active cloned sessions
        this.snapshotData = null;
    }

    async initializeMasterSession() {
        console.log('🎯 Initializing Master Session for Snapshotting...');
        
        // Load existing master session data
        if (!fs.existsSync('master-session-data.json')) {
            throw new Error('Master session data not found. Please run master session first.');
        }
        
        const sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
        console.log('✅ Master session data loaded');
        
        // Launch browser with same configuration as master
        this.masterBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        });
        
        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1280, height: 720 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Restore session state
        await this.restoreSessionState(sessionData);
        
        console.log('✅ Master session initialized for snapshotting');
        return true;
    }

    async restoreSessionState(sessionData) {
        console.log('🔄 Restoring session state...');
        
        // Set cookies
        if (sessionData.cookies && Array.isArray(sessionData.cookies)) {
            await this.masterPage.setCookie(...sessionData.cookies);
            console.log(`🍪 Restored ${sessionData.cookies.length} cookies`);
        }
        
        // Set localStorage and sessionStorage
        await this.masterPage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            if (localStorageData) {
                const localStorage = JSON.parse(localStorageData);
                for (const [key, value] of Object.entries(localStorage)) {
                    window.localStorage.setItem(key, value);
                }
            }
            
            if (sessionStorageData) {
                const sessionStorage = JSON.parse(sessionStorageData);
                for (const [key, value] of Object.entries(sessionStorage)) {
                    window.sessionStorage.setItem(key, value);
                }
            }
        }, sessionData.localStorage, sessionData.sessionStorage);
        
        // Navigate to the authenticated page
        await this.masterPage.goto(sessionData.currentUrl || 'https://one.dat.com/dashboard', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        
        console.log(`📍 Navigated to: ${this.masterPage.url()}`);
    }

    async createSessionSnapshot() {
        console.log('📸 Creating session snapshot...');
        
        if (!this.masterPage) {
            throw new Error('Master session not initialized');
        }
        
        // Capture comprehensive session state
        const snapshotData = await this.masterPage.evaluate(() => {
            return {
                url: window.location.href,
                title: document.title,
                html: document.documentElement.outerHTML,
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                cookies: document.cookie,
                timestamp: new Date().toISOString()
            };
        });
        
        // Get cookies from browser
        const cookies = await this.masterPage.cookies();
        snapshotData.cookiesArray = cookies;
        
        // Store snapshot
        this.snapshotData = snapshotData;
        
        // Save snapshot to file
        fs.writeFileSync('session-snapshot.json', JSON.stringify(snapshotData, null, 2));
        
        console.log('✅ Session snapshot created');
        console.log(`📍 Snapshot URL: ${snapshotData.url}`);
        console.log(`📄 HTML length: ${snapshotData.html.length} chars`);
        console.log(`🍪 Cookies: ${cookies.length}`);
        
        return snapshotData;
    }

    async createClonedSession(sessionId) {
        console.log(`🔄 Creating cloned session: ${sessionId}`);
        
        if (!this.snapshotData) {
            await this.createSessionSnapshot();
        }
        
        // Launch new browser instance for the clone
        const cloneBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        });
        
        const clonePage = await cloneBrowser.newPage();
        await clonePage.setViewport({ width: 1280, height: 720 });
        await clonePage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Restore snapshot state to clone
        await this.restoreSnapshotToClone(clonePage, this.snapshotData);
        
        // Store clone session
        const cloneSession = {
            sessionId,
            browser: cloneBrowser,
            page: clonePage,
            createdAt: new Date(),
            lastAccessed: new Date()
        };
        
        this.activeClones.set(sessionId, cloneSession);
        
        console.log(`✅ Cloned session created: ${sessionId}`);
        return cloneSession;
    }

    async restoreSnapshotToClone(clonePage, snapshotData) {
        console.log('🔄 Restoring snapshot to clone...');
        
        // Set cookies
        if (snapshotData.cookiesArray) {
            await clonePage.setCookie(...snapshotData.cookiesArray);
        }
        
        // Set storage before navigation
        await clonePage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            if (localStorageData) {
                const localStorage = JSON.parse(localStorageData);
                for (const [key, value] of Object.entries(localStorage)) {
                    window.localStorage.setItem(key, value);
                }
            }
            
            if (sessionStorageData) {
                const sessionStorage = JSON.parse(sessionStorageData);
                for (const [key, value] of Object.entries(sessionStorage)) {
                    window.sessionStorage.setItem(key, value);
                }
            }
        }, snapshotData.localStorage, snapshotData.sessionStorage);
        
        // Navigate to the snapshot URL
        await clonePage.goto(snapshotData.url, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        
        console.log(`📍 Clone restored to: ${clonePage.url()}`);
    }

    async getClonedSessionHTML(sessionId) {
        const cloneSession = this.activeClones.get(sessionId);
        
        if (!cloneSession) {
            throw new Error(`Cloned session not found: ${sessionId}`);
        }
        
        // Update last accessed time
        cloneSession.lastAccessed = new Date();
        
        // Get current HTML from clone
        const html = await cloneSession.page.content();
        const url = cloneSession.page.url();
        
        return {
            html,
            url,
            sessionId,
            lastAccessed: cloneSession.lastAccessed
        };
    }

    async cleanupOldSessions(maxAgeMinutes = 30) {
        console.log('🧹 Cleaning up old cloned sessions...');
        
        const now = new Date();
        const sessionsToRemove = [];
        
        for (const [sessionId, cloneSession] of this.activeClones) {
            const ageMinutes = (now - cloneSession.lastAccessed) / (1000 * 60);
            
            if (ageMinutes > maxAgeMinutes) {
                sessionsToRemove.push(sessionId);
            }
        }
        
        for (const sessionId of sessionsToRemove) {
            await this.removeClonedSession(sessionId);
        }
        
        console.log(`🧹 Cleaned up ${sessionsToRemove.length} old sessions`);
    }

    async removeClonedSession(sessionId) {
        const cloneSession = this.activeClones.get(sessionId);
        
        if (cloneSession) {
            await cloneSession.browser.close();
            this.activeClones.delete(sessionId);
            console.log(`🗑️ Removed cloned session: ${sessionId}`);
        }
    }

    async refreshSnapshot() {
        console.log('🔄 Refreshing session snapshot...');
        
        if (this.masterPage) {
            await this.createSessionSnapshot();
            console.log('✅ Snapshot refreshed');
        }
    }

    getActiveSessionsCount() {
        return this.activeClones.size;
    }

    async cleanup() {
        console.log('🧹 Cleaning up Session Snapshot Manager...');
        
        // Close all cloned sessions
        for (const [sessionId, cloneSession] of this.activeClones) {
            await cloneSession.browser.close();
        }
        this.activeClones.clear();
        
        // Close master browser (but don't interfere with actual master session)
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        
        console.log('✅ Session Snapshot Manager cleanup complete');
    }
}

module.exports = SessionSnapshotManager;
