{"url": "https://login.dat.com/u/login/identifier?state=hKFo2SA4UE1ORzVwM29tVDdFczFxaDZGRTZHeDdGMFNxUExHaqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHB1dERCaVlyajBSZWZILVRpMGlJQWtScHRXNm9QQnFlo2NpZNkgZTlsek1YYm5XTkowRDUwQzJoYWFkbzdEaVcxYWt3YUM", "title": "", "html": "<html><head><script>\n    function getOneSignUpUrl() {\n      var originString = window.location.origin;\n      var envAndDomain;\n      if( originString.includes('test')){\n        envAndDomain = \"nprod.dat.com\"\n      } else if( originString.includes('staging')){\n        envAndDomain = \"staging.dat.com\"\n      } else {\n        envAndDomain = \"dat.com\"\n      }\n\n      return \"https://ssu.\" + envAndDomain + \"/?signup=basicwithauthority&product=one_web\";\n    }\n\n    function getOnboardSignUpUrl() {\n      var originString = window.location.origin;\n      var envAndDomain;\n      if( originString.includes('test')){\n        envAndDomain = \"test.nprod.dat.com\"\n      } else if( originString.includes('staging')){\n        envAndDomain = \"staging.dat.com\"\n      } else {\n        envAndDomain = \"dat.com\"\n      }\n\n      return \"https://onboard.\" + envAndDomain + \"/dot\";\n    }\n    function isApplication(appName){\n      return document.evaluate(\"//meta[@dat-app-name='\" + appName + \"']\", document).iterateNext();\n    }\n    function ready() {\n      var signUpBlock = document.evaluate(\"//p[contains(text(), \\\"Don't have an account?\\\")]\", document).iterateNext();\n      var signUpBtn = document.evaluate(\"//a[contains(text(), 'Sign up')]\", document).iterateNext();\n\n      if (!signUpBlock || !signUpBtn) return;\n\n      signUpBlock.style.display = 'none';\n\n      if( signUpBtn && isApplication('freight-dat-one-web')){\n        signUpBlock.style.display = 'block';\n        signUpBtn.href = getOneSignUpUrl();\n      } else if( signUpBtn && isApplication('freight-onboard-web')){\n        signUpBlock.style.display = 'block';\n        signUpBtn.href = getOnboardSignUpUrl();\n      } else if( signUpBtn && isApplication('identity-ssu-web')){\n        signUpBlock.style.display = 'block';\n      }\n    }\n    document.addEventListener('DOMContentLoaded', ready);\n  </script>\n  <meta dat-app-name=\"freight-dat-one-web\">\n  \n  \n  \n    <style>\n      div.background-map {\n        background: linear-gradient(\n            to bottom,\n            rgb(59, 72, 84) 0%,\n            rgb(25, 33, 41) 100%\n          ),\n          url(https://hlp.test.nprod.dat.com/login/assets/map.png);\n        background-blend-mode: multiply;\n        background-position: center center;\n        background-size: cover;\n        height: 100%;\n        width: 100%;\n        z-index: -1;\n        top: 0;\n        left: 0;\n      }\n\n      .error-modal {\n        padding: 24px;\n        border-radius: 5px;\n        width: 600px;\n        font-family: ulp-font, -apple-system, BlinkMacSystemFont, Roboto,\n          Helvetica, sans-serif;\n        color: var(--font-default-color);\n        font-size: 14px;\n        line-height: 24px;\n\n        h2 {\n          font-size: 1.5em;\n          margin-bottom: 0.83em;\n          font-weight: bold;\n        }\n\n        p {\n          margin-block-start: 1em;\n          margin-block-end: 1em;\n        }\n\n        form {\n          text-align: right;\n\n          button {\n            color: #0091ea;\n            cursor: pointer;\n            outline: none;\n            border: none;\n            line-height: 36px;\n            padding: 0 16px;\n            border-radius: 4px;\n            font-size: 14px;\n            font-weight: 500;\n            background: transparent;\n          }\n        }\n      }\n\n      .modal-overlay {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba(0, 0, 0, 0.5);\n        z-index: 10;\n      }\n    </style>\n        <meta charset=\"utf-8\">\n    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n    \n      <meta name=\"ulp-version\" content=\"1.46.1\">\n    \n    \n    \n    <meta name=\"robots\" content=\"noindex, nofollow\">\n    \n    \n    <link rel=\"stylesheet\" href=\"https://cdn.auth0.com/ulp/react-components/1.123.1/css/main_wcag_compliant.cdn.min.css\">\n    <style id=\"custom-styles-container\">\n      \n        \n\n\n\n\n        \n          :root, .af-custom-form-container .af-form {\n    --primary-color: #1B45D7;\n  }\n        \n      \n\n        \n          :root, .af-custom-form-container .af-form {\n    --button-font-color: #ffffff;\n  }\n        \n      \n\n        \n          :root {\n    --secondary-button-border-color: #c9cace;\n    --social-button-border-color: #c9cace;\n    --radio-button-border-color: #c9cace;\n  }\n        \n      \n\n        \n          :root {\n    --secondary-button-text-color: #1e212a;\n  }\n        \n      \n\n        \n          :root {\n    --link-color: #1B45D7;\n  }\n        \n      \n\n        \n          :root {\n    --title-font-color: #1e212a;\n  }\n        \n      \n\n        \n          :root {\n    --font-default-color: #1e212a;\n  }\n        \n      \n\n        \n          :root {\n    --widget-background-color: #ffffff;\n  }\n        \n      \n\n        \n          :root {\n    --box-border-color: #c9cace;\n  }\n        \n      \n\n        \n          :root {\n    --font-light-color: #65676e;\n  }\n        \n      \n\n        \n          :root {\n    --input-text-color: #000000;\n  }\n        \n      \n\n        \n          :root {\n    --input-border-color: #c9cace;\n    --border-default-color: #c9cace;\n  }\n        \n      \n\n        \n          :root {\n    --input-background-color: #ffffff;\n  }\n        \n      \n\n        \n          :root {\n    --icon-default-color: #65676e;\n  }\n        \n      \n\n        \n          :root {\n    --error-color: #e10600;\n    --error-text-color: #ffffff;\n  }\n        \n      \n\n        \n          :root {\n    --success-color: #13a688;\n  }\n        \n      \n\n        \n          :root {\n    --base-focus-color: #1B45D7;\n    --transparency-focus-color: rgba(27,69,215, 0.15);\n  }\n        \n      \n\n        \n          :root {\n    --base-hover-color: #000000;\n    --transparency-hover-color: rgba(0,0,0, var(--hover-transparency-value));\n  }\n        \n      \n\n        \n      \n\n\n\n\n        \n          \n        \n      \n\n        \n          html, :root, .af-custom-form-container .af-form {\n    font-size: 16px;\n    --default-font-size: 16px;\n  }\n        \n      \n\n        \n          body {\n    --title-font-size: 1.5rem;\n    --title-font-weight: var(--font-bold-weight);\n  }\n        \n      \n\n        \n          .c82bde6a5 {\n    font-size: 0.875rem;\n    font-weight: var(--font-default-weight);\n  }\n        \n      \n\n        \n          .ce6a539f4 {\n    font-size: 0.875rem;\n    font-weight: var(--font-default-weight);\n  }\n  .ulp-passkey-benefit-heading {\n    font-size: 1.025rem;\n  }\n        \n      \n\n        \n          .c8ccf545f, .c432a676c {\n    font-size: 1rem;\n    font-weight: var(--font-default-weight);\n  }\n        \n      \n\n        \n          body {\n    --ulp-label-font-size: 1rem;\n    --ulp-label-font-weight: var(--font-default-weight);\n  }\n        \n      \n\n        \n          .c85106b14, .ce9b0ef1a, [id^='ulp-container-'] a {\n    font-size: 0.875rem;\n    font-weight: var(--font-bold-weight) !important;\n  }\n        \n      \n\n        \n          \n        \n      \n\n\n\n\n        \n          :root {\n    --button-border-width: 1px;\n    --social-button-border-width: 1px;\n    --radio-border-width: 1px;\n  }\n        \n      \n\n        \n          body {\n    --button-border-radius: 9999px;\n    --radio-border-radius: 9999px;\n  }\n        \n      \n\n        \n          :root {\n    --input-border-width: 1px;\n  }\n        \n      \n\n        \n          body {\n    --input-border-radius: 10px;\n  }\n\n  .af-custom-form-container .af-form {\n    --border-radius: 10px;\n  }\n        \n      \n\n        \n          :root {\n    --border-radius-outer: 5px;\n  }\n        \n      \n\n        \n          :root {\n    --box-border-width: 0px;\n  }\n        \n      \n\n        \n          \n        \n      \n\n\n\n\n        \n          \n    body {\n      --logo-alignment: 0 auto 0 0;\n    }\n  \n        \n      \n\n        \n          \n    .c4ad5475d {\n      content: url('https://hlp.dat.com/login/assets/dat-logo-email.svg');\n    }\n  \n        \n      \n\n        \n          body {\n    --logo-height: 42px;\n  }\n  .c4ad5475d {\n    height: var(--logo-height);\n  }\n  \n        \n      \n\n        \n          \n    body {\n      --header-alignment: center;\n    }\n  \n        \n      \n\n        \n          \n        \n      \n\n\n\n\n        \n          .c2c6112f9 {\n    --page-background-alignment: center;\n  }\n        \n      \n\n        \n          body {\n    --page-background-color: #1c2128;\n  }\n        \n      \n\n        \n          .c2c6112f9 {\n    --page-background-image: url('https://hlp.prod.dat.com/login/assets/map-gray.png');\n  }\n        \n      \n\n\n\n\n      \n    </style>\n    <style>\n    /* By default, hide features for javascript-disabled browsing */\n    /* We use !important to override any css with higher specificity */\n    /* It is also overriden by the styles in <noscript> in the header file */\n    .no-js {\n      clip: rect(0 0 0 0);\n      clip-path: inset(50%);\n      height: 1px;\n      overflow: hidden;\n      position: absolute;\n      white-space: nowrap;\n      width: 1px;\n    }\n  </style>\n  <noscript>\n    <style>\n      /* We use !important to override the default for js enabled */\n      /* If the display should be other than block, it should be defined specifically here */\n      .js-required { display: none !important; }\n      .no-js {\n        clip: auto;\n        clip-path: none;\n        height: auto;\n        overflow: auto;\n        position: static;\n        white-space: normal;\n        width: var(--prompt-width);\n      }\n    </style>\n  </noscript>\n    \n</head>\n  \n\n    <body class=\"_widget-auto-layout\">\n      \n    \n    \n    <div class=\"background-map\"><main class=\"_widget login-id\">\n  <section class=\"c12e3321f _prompt-box-outer c77522294\">\n    <div class=\"ca317578c c68120e2c\">\n      \n    \n      \n    \n      <div class=\"ce25735de\">\n        <header class=\"cc268569a c873a54b6\">\n          <div title=\"DAT Solutions\" id=\"custom-prompt-logo\" style=\"width: auto !important; height: 60px !important; position: static !important; margin: auto !important; padding: 0 !important; background-color: transparent !important; background-position: center !important; background-size: contain !important; background-repeat: no-repeat !important\"></div>\n        \n          <img class=\"c4ad5475d c6f707b54\" id=\"prompt-logo-center\" src=\"https://hlp.prod.prod.dat.com/login/assets/dat-logo-email.svg\" alt=\"DAT Solutions\">\n        \n          \n            <h1 class=\"cf396e333 c79660e4f\">Log In</h1>\n          \n        \n          <div class=\"c82bde6a5 c2723e7c2\">\n            <p class=\"c1a53a629 cad87213d\">To continue to your DAT account</p>\n          </div>\n        </header>\n      \n        <div class=\"ce6a539f4 c1bb4fb4c\">\n          \n        \n          \n            <div class=\"cf79069f4 c322753b2\">\n              <div class=\"c1df68bcf\">\n                \n              \n                \n                  <form method=\"POST\" class=\"c071feae0 _form-login-id\" data-form-primary=\"true\" data-disable-html-validations=\"true\" novalidate=\"\">\n                    \n                  \n                    <input type=\"hidden\" name=\"state\" value=\"hKFo2SA4UE1ORzVwM29tVDdFczFxaDZGRTZHeDdGMFNxUExHaqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHB1dERCaVlyajBSZWZILVRpMGlJQWtScHRXNm9QQnFlo2NpZNkgZTlsek1YYm5XTkowRDUwQzJoYWFkbzdEaVcxYWt3YUM\">\n                  \n                    \n                  \n                    \n                  \n                    <div class=\"cf79069f4 c322753b2\">\n                      <div class=\"c1df68bcf\">\n                        \n                      \n                        \n                          \n                            <div class=\"input-wrapper _input-wrapper\">\n                              <div class=\"c1543d719 c33a85340 text cd7e2cdcb ulp-field c18f1a9ee focus\" data-action-text=\"\" data-alternate-action-text=\"\">\n                                <label aria-hidden=\"true\" class=\"c50a7baf5 cb8426308 ceb270847\" for=\"username\">\n                                  Email address*\n                                </label>\n                              \n                                <input class=\"input c04aa24a5 cf229b4de\" inputmode=\"email\" name=\"username\" id=\"username\" type=\"text\" aria-label=\"Email address\" aria-required=\"true\" value=\"\" required=\"\" autocomplete=\"email\" autocapitalize=\"none\" spellcheck=\"false\" autofocus=\"\">\n                              </div>\n                            \n                              \n                            \n                              \n                                <div id=\"error-cs-email-required\" class=\"ulp-error-info aria-error-check\" data-ulp-validation-function=\"ulpRequiredFunction\" data-ulp-validation-event-listeners=\"blur,change,input,focus\" data-ulp-validation-target=\"username\">Please enter an email address</div>\n                              \n                            \n                              \n                                <div id=\"error-cs-email-invalid\" class=\"ulp-error-info aria-error-check\" data-ulp-validation-function=\"ulpEmailValidationFunction\" data-ulp-validation-event-listeners=\"blur,change,input,focus\" data-ulp-validation-target=\"username\">Email is not valid</div>\n                              \n                            \n                              \n                                <div id=\"error-cs-pattern-mismatch\" class=\"ulp-error-info aria-error-check\" data-ulp-validation-function=\"ulpPatternCheckFunction\" data-ulp-validation-event-listeners=\"blur,change,input,focus\" data-ulp-validation-target=\"username\">The username has invalid characters.</div>\n                              \n                            \n                              \n                            </div>\n                          \n                        \n                      \n                        \n                      \n                        \n                      </div>\n                    </div>\n                  \n                    \n                  \n                    <input class=\"hide\" type=\"password\" autocomplete=\"off\" tabindex=\"-1\" aria-hidden=\"true\">\n                  \n                    <input type=\"hidden\" id=\"js-available\" name=\"js-available\" value=\"true\">\n                  \n                    <input type=\"hidden\" id=\"webauthn-available\" name=\"webauthn-available\" value=\"true\">\n                  \n                    <input type=\"hidden\" id=\"is-brave\" name=\"is-brave\" value=\"false\">\n                  \n                    <input type=\"hidden\" id=\"webauthn-platform-available\" name=\"webauthn-platform-available\" value=\"false\">\n                  \n                    \n                  \n                    <div class=\"c7a622d6a\">\n                      \n                        <button type=\"submit\" name=\"action\" value=\"default\" class=\"c8ccf545f cd30107ea cb74f79c9 cba4da699 _button-login-id\" data-action-button-primary=\"true\">CONTINUE</button>\n                      \n                    </div>\n                  </form>\n                \n              </div>\n            </div>\n          \n        \n          \n        \n          \n            <div class=\"ulp-alternate-action  _alternate-action __s16nu9\">\n              <p class=\"c1a53a629 cad87213d c61e40766\" style=\"display: block;\">Don't have an account?\n                <a class=\"ce9b0ef1a c99384160\" href=\"https://ssu.dat.com/?signup=basicwithauthority&amp;product=one_web\">Sign up</a>\n              </p>\n            </div>\n          \n        \n          \n        \n          \n        \n          \n        \n          \n        </div>\n      </div>\n    </div>\n  \n    \n  </section>\n</main>\n<script id=\"client-scripts\">\nwindow.ulpFlags = {\"enable_ulp_wcag_compliance\":true,\"enable_ulp_rtl_support\":false};!function(){var e,t,C,S,F,T,k,L,n,r,a={exports:function(e,t){return\"object\"==typeof e.ulpFlags&&null!==e.ulpFlags?e.ulpFlags:{}}}.exports(window,document),i=((e={}).exports=function(n,a){var r={},i={};function u(e,t){if(e.classList)return e.classList.add(t);var n=e.className.split(\" \");-1===n.indexOf(t)&&(n.push(t),e.className=n.join(\" \"))}function o(e,t,n,r){return e.addEventListener(t,n,r)}function c(e){return\"string\"==typeof e}function l(e,t){return c(e)?a.querySelector(e):e.querySelector(t)}function s(e,t){if(e.classList)return e.classList.remove(t);var n=e.className.split(\" \"),r=n.indexOf(t);-1!==r&&(n.splice(r,1),e.className=n.join(\" \"))}function f(e,t){return e.getAttribute(t)}function d(e,t,n){return e.setAttribute(t,n)}function p(e){return e.remove()}var e=[\"text\",\"number\",\"email\",\"password\",\"tel\",\"url\"],t=\"select,textarea,\"+e.map(function(e){return'input[type=\"'+e+'\"]'}).join(\",\");return{addClass:u,toggleClass:function(e,t,n){if(!0===n||!1===n)return r=e,a=t,!0!==n?s(r,a):u(r,a);var r,a;if(e.classList)return e.classList.toggle(t);var i=e.className.split(\" \"),o=i.indexOf(t);-1!==o?i.splice(o,1):i.push(t),e.className=i.join(\" \")},hasClass:function(e,t){return e.classList?e.classList.contains(t):-1!==e.className.split(\" \").indexOf(t)},addClickListener:function(e,t){return o(e,\"click\",t)},addEventListener:o,getAttribute:f,hasAttribute:function(e,t){return e.hasAttribute(t)},getElementById:function(e){return a.getElementById(e)},getParent:function(e){return e.parentNode},isString:c,loadScript:function(e,t){var n=a.createElement(\"script\");for(var r in t)r.startsWith(\"data-\")?n.dataset[r.replace(\"data-\",\"\")]=t[r]:n[r]=t[r];n.src=e,a.body.appendChild(n)},removeScript:function(e){a.querySelectorAll('script[src=\"'+e+'\"]').forEach(function(e){e.remove()})},poll:function(e){var i=e.interval||2e3,t=e.url||n.location.href,o=e.condition||function(){return!0},u=e.onSuccess||function(){},c=e.onError||function(){};return setTimeout(function r(){var a=new XMLHttpRequest;return a.open(\"GET\",t),a.setRequestHeader(\"Accept\",\"application/json\"),a.onload=function(){if(200===a.status){var e=\"application/json\"===a.getResponseHeader(\"Content-Type\").split(\";\")[0]?JSON.parse(a.responseText):a.responseText;return o(e)?u():setTimeout(r,i)}if(429!==a.status)return c({status:a.status,responseText:a.responseText});var t=1e3*Number.parseInt(a.getResponseHeader(\"X-RateLimit-Reset\")),n=t-(new Date).getTime();return setTimeout(r,i<n?n:i)},a.send()},i)},querySelector:l,querySelectorAll:function(e,t){var n=c(e)?a.querySelectorAll(e):e.querySelectorAll(t);return Array.prototype.slice.call(n)},removeClass:s,removeElement:p,setAttribute:d,removeAttribute:function(e,t){return e.removeAttribute(t)},swapAttributes:function(e,t,n){var r=f(e,t),a=f(e,n);d(e,n,r),d(e,t,a)},setGlobalFlag:function(e,t){r[e]=!!t},getGlobalFlag:function(e){return!!r[e]},setSubmittedForm:function(e,t){i[e]=t},getSubmittedForm:function(e){return i[e]},preventFormSubmit:function(e){e.stopPropagation(),e.preventDefault()},matchMedia:function(e){return\"function\"!=typeof n.matchMedia&&n.matchMedia(e).matches},dispatchEvent:function(e,t,n){var r;\"function\"!=typeof Event?(r=a.createEvent(\"Event\")).initCustomEvent(t,n,!1):r=new Event(t,{bubbles:n}),e.dispatchEvent(r)},timeoutPromise:function(e,a){return new Promise(function(t,n){var r=setTimeout(function(){n(new Error(\"timeoutPromise: promise timed out\"))},e);a.then(function(e){clearTimeout(r),t(e)},function(e){clearTimeout(r),n(e)})})},createMutationObserver:function(e){return\"undefined\"==typeof MutationObserver?null:new MutationObserver(e)},consoleWarn:function(){(console.warn||console.log).apply(console,arguments)},getConfigJson:function(e){try{var t=l(e);if(!t)return null;var n=t.value;return n?JSON.parse(n):null}catch(e){return null}},getCSSVariable:function(e){return getComputedStyle(a.documentElement).getPropertyValue(e)},removeAndTrimString:function(e,t){var n=new RegExp(t,\"g\"),r=e.replace(n,\"\");return r=r.replace(/\\s+/g,\"  \").trim()},htmlEncode:function(e){var t=a.createTextNode(e),n=a.createElement(\"span\");return n.appendChild(t),n.innerHTML||\"\"},cleanServerErrorMessage:function(e,t){0<e.length&&0<t.length&&t.forEach(function(e){p(e)})},setTimeout:setTimeout,globalWindow:n,SUPPORTED_INPUT_TYPES:e,ELEMENT_TYPE_SELECTOR:t,RUN_INIT:!0}},e.exports)(window,document),o=function(){var e={};function v(e){if(!(\"string\"==typeof e||e instanceof String)){var t=typeof e;throw null===e?t=\"null\":\"object\"===t&&(t=e.constructor.name),new TypeError(\"Expected a string but received a \"+t)}}function m(e,t){var n,r;v(e),r=\"object\"==typeof t?(n=t.min||0,t.max):(n=t,arguments[2]);var a=encodeURI(e).split(/%..|./).length-1;return n<=a&&(void 0===r||a<=r)}function h(e,t){for(var n in void 0===e&&(e={}),t)void 0===e[n]&&(e[n]=t[n]);return e}var g={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1},t=\"(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\",n=\"(\"+t+\"[.]){3}\"+t,r=new RegExp(\"^\"+n+\"$\"),a=\"(?:[0-9a-fA-F]{1,4})\",i=new RegExp(\"^((?:\"+a+\":){7}(?:\"+a+\"|:)|(?:\"+a+\":){6}(?:\"+n+\"|:\"+a+\"|:)|(?:\"+a+\":){5}(?::\"+n+\"|(:\"+a+\"){1,2}|:)|(?:\"+a+\":){4}(?:(:\"+a+\"){0,1}:\"+n+\"|(:\"+a+\"){1,3}|:)|(?:\"+a+\":){3}(?:(:\"+a+\"){0,2}:\"+n+\"|(:\"+a+\"){1,4}|:)|(?:\"+a+\":){2}(?:(:\"+a+\"){0,3}:\"+n+\"|(:\"+a+\"){1,5}|:)|(?:\"+a+\":){1}(?:(:\"+a+\"){0,4}:\"+n+\"|(:\"+a+\"){1,6}|:)|(?::((?::\"+a+\"){0,5}:\"+n+\"|(?::\"+a+\"){1,7}|:)))(%[0-9a-zA-Z-.:]{1,})?$\");function b(e,t){return void 0===t&&(t=\"\"),v(e),(t=String(t))?\"4\"===t?r.test(e):\"6\"===t&&i.test(e):b(e,4)||b(e,6)}var w={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:\"\",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},y=/^([^\\x00-\\x1F\\x7F-\\x9F\\cX]+)</i,_=/^[a-z\\d!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]+$/i,x=/^[a-z\\d]+$/,E=/^([\\s\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f\\x21\\x23-\\x5b\\x5d-\\x7e]|(\\\\[\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]))*$/i,A=/^[a-z\\d!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~\\u00A1-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+$/i,C=/^([\\s\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f\\x21\\x23-\\x5b\\x5d-\\x7e\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]|(\\\\[\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]))*$/i,S=254;function o(e,t){if(v(e),(t=h(t,w)).require_display_name||t.allow_display_name){var n=e.match(y);if(n){var r=n[1];if(e=e.replace(r,\"\").replace(/(^<|>$)/g,\"\"),r.endsWith(\" \")&&(r=r.slice(0,-1)),!function(e){var t=e.replace(/^\"(.+)\"$/,\"$1\");if(!t.trim())return!1;if(/[\\.\";<>]/.test(t)){if(t===e)return!1;if(t.split('\"').length!==t.split('\\\\\"').length)return!1}return!0}(r))return!1}else if(t.require_display_name)return!1}if(!t.ignore_max_length&&e.length>S)return!1;var a=e.split(\"@\"),i=a.pop(),o=i.toLowerCase();if(t.host_blacklist.includes(o))return!1;if(0<t.host_whitelist.length&&!t.host_whitelist.includes(o))return!1;var u=a.join(\"@\");if(t.domain_specific_validation&&(\"gmail.com\"===o||\"googlemail.com\"===o)){var c=(u=u.toLowerCase()).split(\"+\")[0];if(!m(c.replace(/\\./g,\"\"),{min:6,max:30}))return!1;for(var l=c.split(\".\"),s=0;s<l.length;s++)if(!x.test(l[s]))return!1}if(!(!1!==t.ignore_max_length||m(u,{max:64})&&m(i,{max:254})))return!1;if(!function(e,t){v(e),(t=h(t,g)).allow_trailing_dot&&\".\"===e[e.length-1]&&(e=e.substring(0,e.length-1)),!0===t.allow_wildcard&&0===e.indexOf(\"*.\")&&(e=e.substring(2));var n=e.split(\".\"),r=n[n.length-1];if(t.require_tld){if(n.length<2)return!1;if(!t.allow_numeric_tld&&!/^([a-z\\u00A1-\\u00A8\\u00AA-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(r))return!1;if(/\\s/.test(r))return!1}return!(!t.allow_numeric_tld&&/^\\d+$/.test(r))&&n.every(function(e){return!(63<e.length&&!t.ignore_max_length||!/^[a-z_\\u00a1-\\uffff0-9-]+$/i.test(e)||/[\\uff01-\\uff5e]/.test(e)||/^-|-$/.test(e)||!t.allow_underscores&&/_/.test(e))})}(i,{require_tld:t.require_tld,ignore_max_length:t.ignore_max_length,allow_underscores:t.allow_underscores})){if(!t.allow_ip_domain)return!1;if(!b(i)){if(!i.startsWith(\"[\")||!i.endsWith(\"]\"))return!1;var f=i.slice(1,-1);if(0===f.length||!b(f))return!1}}if('\"'===u[0])return u=u.slice(1,u.length-1),t.allow_utf8_local_part?C.test(u):E.test(u);for(var d=t.allow_utf8_local_part?A:_,p=(l=u.split(\".\"),0);p<l.length;p++)if(!d.test(l[p]))return!1;return!t.blacklisted_chars||-1===u.search(new RegExp(\"[\"+t.blacklisted_chars+\"]+\",\"g\"))}return e.exports=function(e,t){return{ulpRequiredFunction:function(e,t){return!t||!!e.value},ulpEmailValidationFunction:function(e,t){return!t||!e.value||!!o(e.value)},ulpPatternCheckFunction:function(e,t){return!t||!e.value||function(e){if(\"password\"===e.name)return!0;var t=e.getAttribute(\"pattern\");return!t||null!==e.value.match(t)}(e)}}},e.exports}()(window,document),u={exports:function(e,t){for(var i=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\",s=new Uint8Array(256),o=0;o<i.length;o++)s[i.charCodeAt(o)]=o;function u(e){var t,n=new Uint8Array(e),r=n.length,a=\"\";for(t=0;t<r;t+=3)a+=i[n[t]>>2],a+=i[(3&n[t])<<4|n[t+1]>>4],a+=i[(15&n[t+1])<<2|n[t+2]>>6],a+=i[63&n[t+2]];return r%3==2?a=a.substring(0,a.length-1):r%3==1&&(a=a.substring(0,a.length-2)),a}function n(){return navigator&&navigator.credentials&&\"undefined\"!=typeof PublicKeyCredential}return{base64URLEncode:u,base64URLDecode:function(e){var t,n,r,a,i,o=.75*e.length,u=e.length,c=0,l=new Uint8Array(o);for(t=0;t<u;t+=4)n=s[e.charCodeAt(t)],r=s[e.charCodeAt(t+1)],a=s[e.charCodeAt(t+2)],i=s[e.charCodeAt(t+3)],l[c++]=n<<2|r>>4,l[c++]=(15&r)<<4|a>>2,l[c++]=(3&a)<<6|63&i;return l.buffer},publicKeyCredentialToJSON:function e(t){if(t instanceof Array){var n=[];for(o=0;o<t.length;o+=1)n.push(e(t[o]));return n}if(t instanceof ArrayBuffer)return u(t);if(t instanceof Object){var r={};for(var a in t)r[a]=e(t[a]);return r}return t},str2ab:function(e){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),r=0,a=e.length;r<a;r++)n[r]=e.charCodeAt(r);return t},isWebAuthnAvailable:n,isWebauthnPlatformAuthenticatorAvailableAsync:function(e){return n()?e(1e3,PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()):Promise.resolve(!1)}}}}.exports(window,document);({exports:function(e,r,a,t){var n=e(\".cd69bf957\"),i=e(\"#alert-trigger\"),o=e(\".c604cf44c\"),u=e(\".c36eff428\"),c=!1;i&&u&&n&&t(n,function(e){var t=e.target===i,n=u.contains(e.target);return t&&!c?(r(o,\"show\"),void(c=!0)):t&&c||c&&!n?(a(o,\"show\"),void(c=!1)):void 0})}}).exports(i.querySelector,i.addClass,i.removeClass,i.addClickListener),(C=\"recaptcha_v2\",S=\"recaptcha_enterprise\",F=\"hcaptcha\",T=\"friendly_captcha\",k=\"arkose\",L=\"auth0_v2\",(t={}).exports=function(i,o,a,u,c,l,s,f){var d=500,p=3,v=0,m=a(\"div[data-captcha-sitekey]\"),h=a(\".ulp-captcha-client-error\"),t=a(\"div[data-captcha-sitekey] input\");function g(){switch(y()){case C:return window.grecaptcha;case S:return window.grecaptcha.enterprise;case F:return window.hcaptcha;case T:return window.friendlyChallenge;case k:return window.arkose;case L:return window.turnstile}}function b(){return a(function(){switch(y()){case C:case S:return\"#ulp-recaptcha\";case F:return\"#ulp-hcaptcha\";case T:return\"#ulp-friendly-captcha\";case k:return\"#ulp-arkose\";case L:return\"#ulp-auth0-v2-captcha\"}}())}function w(){return m.getAttribute(\"data-captcha-lang\")}function y(){return m.getAttribute(\"data-captcha-provider\")}function _(){return m.getAttribute(\"data-captcha-sitekey\")}function x(){return a('form[data-form-primary=\"true\"]')}function E(e){return t.value=e}function A(){var e=g(),t=l(\"--ulp-captcha-widget-theme\")||\"light\";if(y()===T)\"dark\"===t&&u(a(\".frc-captcha\"),\"dark\"),(r=e.autoWidget).opts.language=w(),r.opts.doneCallback=function(e){E(e)};else{var n={sitekey:_(),theme:t,\"expired-callback\":function(){E(\"\"),u(m,\"ca5d758a7\"),e.reset(r)},callback:function(e){E(e),c(m,\"ca5d758a7\")}};y()===L&&(n.language=w(),n.retry=\"never\",n.size=\"flexible\",n[\"response-field\"]=!1,n[\"error-callback\"]=function(e){return console.error(\"ERROR: Auth Challenge Error Code\",e),E(\"\"),v<p?(v++,g().reset(r)):(h.innerHTML=h.innerHTML.replace(\"#{errorCode}\",f(e)),u(m,\"ca5d758a7\"),c(h,\"hide\")),!0});var r=e.render(b(),n)}}m&&function(){var e=\"captchaCallback_\"+Math.floor(1000001*Math.random()),t=y(),n={async:!0,defer:!0},r=function(e,t,n,r){switch(y()){case C:return\"https://www.recaptcha.net/recaptcha/api.js?render=explicit&hl=\"+e+\"&onload=\"+t;case S:return\"https://www.recaptcha.net/recaptcha/enterprise.js?render=explicit&hl=\"+e+\"&onload=\"+t;case F:return\"https://js.hcaptcha.com/1/api.js?render=explicit&hl=\"+e+\"&onload=\"+t;case T:return\"https://cdn.jsdelivr.net/npm/friendly-challenge@0.9.14/widget.min.js\";case k:return\"https://\"+n+\".arkoselabs.com/v2/\"+r+\"/api.js\";case L:return\"https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit&onload=\"+t}}(w(),e,m.getAttribute(\"data-captcha-client-subdomain\"),_());if(t===k||t===L){n[\"data-callback\"]=e,n.onerror=function(){if(v<p)return o(r),i(r,n),void v++;o(r),E(\"BYPASS_CAPTCHA\")};var a=function(e){var t,n;t=e,n=function(e){setTimeout(function(){t.run()},d),e.preventDefault()},x().addEventListener(\"submit\",n),t.setConfig({onCompleted:function(e){E(e.token),x().submit()},onError:function(e){return fetch(\"https://status.arkoselabs.com/api/v2/status.json\").then(function(e){return e.json()}).then(function(e){var t=e.status.indicator;return\"none\"===t}).catch(function(e){return!1}).then(function(e){if(e&&v<p)return t.reset(),new Promise(function(e){setTimeout(function(){e(t.run())},d),v++});E(\"BYPASS_CAPTCHA\"),x().removeEventListener(\"submit\",n)})}})};t===L&&(a=function(){A()}),window[e]=a}else window[e]=function(){delete window[e],A()},t===T&&(u(b(),\"frc-captcha\"),s(b(),\"data-sitekey\",_()),n.onload=window[e]);i(r,n)}()},t.exports)(i.loadScript,i.removeScript,i.querySelector,i.addClass,i.removeClass,i.getCSSVariable,i.setAttribute,i.htmlEncode),((n={}).exports=function(r,e,a,i,o,u,c,l,n,s,t){if(!t.enable_ulp_wcag_compliance){if(r(\"body._simple-labels\"))return e(\".c50a7baf5.no-js\").forEach(function(e){o(e,\"no-js\")}),void e(\".c50a7baf5.js-required\").forEach(function(e){i(e,\"hide\")});e(\".c1543d719:not(.ce47f5a80):not(disabled)\").forEach(function(e){i(e,\"c18f1a9ee\");var t,n=r(e,\".input\");n.value&&i(e,\"c1244e32d\"),a(e,\"change\",f),a(n,\"blur\",f),a(n,\"animationstart\",d),t=n,c(function(){t.value&&l(t,\"change\",!0)},100)})}function f(e){var t=e.target,n=u(t);t.value||s(t,\"data-autofilled\")?i(n,\"c1244e32d\"):o(n,\"c1244e32d\")}function d(e){var t=e.target;\"onAutoFillStart\"===e.animationName&&(n(t,\"data-autofilled\",!0),l(e.target,\"change\",!0),a(t,\"keyup\",p,{once:!0}))}function p(e){var t=e.target;n(t,\"data-autofilled\",\"\")}},n.exports)(i.querySelector,i.querySelectorAll,i.addEventListener,i.addClass,i.removeClass,i.getParent,i.setTimeout,i.dispatchEvent,i.setAttribute,i.getAttribute,a),{exports:function(e,t,r,a,i,o,u,c){function n(e){var t=r(\"submitted\"),n=i(\"submittedForm\");a(\"submitted\",!0),o(\"submittedForm\",e.currentTarget),t&&n&&n===e.currentTarget?u(e):\"apple\"===c(e.target,\"data-provider\")&&setTimeout(function(){a(\"submitted\",!1)},2e3)}var l=e(\"form\");l&&l.forEach(function(e){t(e,\"submit\",n)})}}.exports(i.querySelectorAll,i.addEventListener,i.getGlobalFlag,i.setGlobalFlag,i.getSubmittedForm,i.setSubmittedForm,i.preventFormSubmit,i.getAttribute),{exports:function(t,e,n){var r=t(\"form._form-detect-browser-capabilities\"),a=t(\"main.login-id\"),i=t(\"main.signup-with-passkeys\"),o=t(\"div#passkey-detect-browser-capabilities\");if(r||a||i||o){var u=e.isWebAuthnAvailable();t(\"#webauthn-available\").value=u?\"true\":\"false\",t(\"#js-available\").value=\"true\",navigator.brave?navigator.brave.isBrave().then(function(e){t(\"#is-brave\").value=e,c()}):c()}function c(){u?e.isWebauthnPlatformAuthenticatorAvailableAsync(n).then(function(e){t(\"#webauthn-platform-available\").value=e?\"true\":\"false\",r&&r.submit()}).catch(function(e){t(\"#webauthn-platform-available\").value=\"false\",r&&r.submit()}):(t(\"#webauthn-platform-available\").value=\"false\",r&&r.submit())}}}.exports(i.querySelector,u,i.timeoutPromise),function(){var e={};function u(e,n,r,t,a,i){var o=\"user-initiated-passkey-challenge\",u=n(\"#allow-passkeys\");if(u&&u.value){var c=a(\"#passkey-config-json\");if(c){var l=n(\".passkey-begin-button\"),s=r.isWebauthnPlatformAuthenticatorAvailableAsync(t),f=n(\"#username\"),d=c,p=new AbortController;d.publicKey.challenge=r.base64URLDecode(d.publicKey.challenge),l&&e(l,function(e){l.disabled=!0,p.abort(o),navigator.credentials.get(d).then(function(e){var t=g(e);s.then(function(e){t.isUserVerifyingPlatformAuthenticatorAvailable=e,h(JSON.stringify(t)),v()})}).catch(function(e){m(e)})}),window.PublicKeyCredential&&window.PublicKeyCredential.isConditionalMediationAvailable&&window.PublicKeyCredential.isConditionalMediationAvailable().then(function(e){if(e){var t=Object.assign({},d,{mediation:\"conditional\",signal:p.signal});navigator.credentials.get(t).then(function(e){f.blur();var t=g(e);s.then(function(e){t.isUserVerifyingPlatformAuthenticatorAvailable=e,h(JSON.stringify(t)),v()})}).catch(function(e){e!==o&&\"AbortError\"!==e.name&&i(\"autofill UI error\",e)})}}).catch(function(){})}}function v(){n(\"form._form-passkey-challenge\").submit()}function m(e){var t;n(\"#action\").value=\"showError::\"+(t=e,JSON.stringify({name:t.name,message:t.message,stack:t.stack})),n(\"form._form-passkey-challenge\").submit()}function h(e){n(\"#passkey\").value=e}function g(e){var t={id:e.id,rawId:r.base64URLEncode(e.rawId),type:e.type,response:{clientDataJSON:r.base64URLEncode(e.response.clientDataJSON),authenticatorData:r.base64URLEncode(e.response.authenticatorData),signature:r.base64URLEncode(e.response.signature),userHandle:e.response.userHandle?r.base64URLEncode(e.response.userHandle):null}};return e.authenticatorAttachment&&(t.authenticatorAttachment=e.authenticatorAttachment),t}}return e.exports=function(e,t,n,r,a,i,o){return e(window,\"load\",function(e){u(t,n,r,a,i,o)}),{configurePasskeys:u}},e.exports}()(i.addEventListener,i.addClickListener,i.querySelector,u,i.timeoutPromise,i.getConfigJson,i.consoleWarn),{exports:function(t,e,n){var r=e(\".passkey-hidden-ui\"),a=navigator.userAgent.toLowerCase(),i=!!window.safari||a.match(/safari/)&&!a.match(/chrome/)&&!a.match(/chromium/);!r||r.length<=0||(i?r.forEach(function(e){t(e,\"passkey-hidden-ui\")}):window.PublicKeyCredential&&window.PublicKeyCredential.isConditionalMediationAvailable&&window.PublicKeyCredential.isConditionalMediationAvailable().then(function(e){e?r.forEach(function(e){n(e)}):r.forEach(function(e){t(e,\"passkey-hidden-ui\")})}).catch(function(e){r.forEach(function(e){t(e,\"passkey-hidden-ui\")})}))}}.exports(i.removeClass,i.querySelectorAll,i.removeElement),{exports:function(n,e,r,a,i,o,u,c,l,t,s,f){if(f.enable_ulp_wcag_compliance){var d=e(\"[id^='ulp-container-']\");if(d&&d.length){var p=t(_);if(p)for(var v=0;v<d.length;v++)p.observe(d[v],{childList:!0,subtree:!0})}_()}function m(e){var t=e.target,n=o(t);t.value||c(t,\"data-autofilled\")?a(n,\"c1244e32d\"):i(n,\"c1244e32d\")}function h(e){var t=e.target,n=o(t);a(n,\"focus\"),y(t,n)}function g(e){var t=e.target,n=o(t);i(n,\"focus\"),m(e),y(t,n)}function b(e){var t=e.target;l(t,\"data-autofilled\",\"\")}function w(e){var t=e.target;\"onAutoFillStart\"===e.animationName&&(l(t,\"data-autofilled\",!0),dispatchEvent(e.target,\"change\",!0),r(t,\"keyup\",b,{once:!0}))}function y(e,t){e.value?a(t,\"c1244e32d\"):i(t,\"c1244e32d\")}function _(){e(\".ulp-field\").forEach(function(e){if(!u(e,\"c18f1a9ee\")){var t=n(e,s);t&&(a(e,\"c18f1a9ee\"),y(t,e),setTimeout(function(){y(t,e)},50),t===document.activeElement&&a(e,\"focus\"),r(t,\"change\",m),r(t,\"focus\",h),r(t,\"blur\",g),r(t,\"animationstart\",w))}})}}}.exports(i.querySelector,i.querySelectorAll,i.addEventListener,i.addClass,i.removeClass,i.getParent,i.hasClass,i.getAttribute,i.setAttribute,i.createMutationObserver,i.ELEMENT_TYPE_SELECTOR,a),{exports:function(n,e,r,a,i,o,u,t,c,l){if(!l.enable_ulp_wcag_compliance){var s=e(\"[id^='ulp-container-']\");if(s&&s.length){var f=t(g);if(f)for(var d=0;d<s.length;d++)f.observe(s[d],{childList:!0,subtree:!0});g()}}function p(e){var t=e.target,n=o(t);t.value?a(n,\"c1244e32d\"):i(n,\"c1244e32d\")}function v(e){var t=e.target,n=o(t);a(n,\"focus\"),h(t,n)}function m(e){var t=e.target,n=o(t);i(n,\"focus\"),h(t,n)}function h(e,t){e.value?a(t,\"c1244e32d\"):i(t,\"c1244e32d\")}function g(){e(\"[id^='ulp-container-'] .ulp-field\").forEach(function(e){if(!u(e,\"c18f1a9ee\")){var t=n(e,c);t&&(a(e,\"c18f1a9ee\"),h(t,e),setTimeout(function(){h(t,e)},50),t===document.activeElement&&a(e,\"focus\"),r(t,\"change\",p),r(t,\"focus\",v),r(t,\"blur\",m))}})}}}.exports(i.querySelector,i.querySelectorAll,i.addEventListener,i.addClass,i.removeClass,i.getParent,i.hasClass,i.createMutationObserver,i.ELEMENT_TYPE_SELECTOR,a),{exports:function(r,o,a,i,u,c,l,s,f,d,p,v,t,m,h,e,n,g){if(g.enable_ulp_wcag_compliance){var b=!1,w=e+',input[type=\"checkbox\"]';return F(),[w,y,_,x,E,A,C,S,F]}function y(e){var t=u(e,\"data-ulp-validation-function\"),n=i(e);return{functionName:t,element:r(n,w),parent:n}}function _(e){var a=[],i=[];return o(e,\"[data-ulp-validation-function]\").forEach(function(e){var t=y(e),n=[];if(t.element){if(\"input\"===t.element.tagName.toLowerCase()){var r=u(t.element,\"type\");\"checkbox\"!==r&&-1===h.indexOf(r)&&n.push(\"Unsupported input type: \"+r)}}else n.push(\"Could not find element\");v[t.functionName]||n.push(\"Could not find function with name: \"+t.functionName),n.length?i=i.concat(n):a.push(e)}),i.length&&t(i.join(\"\\r\\n\")),a}function x(e,t,n){var r=y(e),a=(0,v[r.functionName])(r.element,t,n);a?s(e,\"ulp-validator-error\")&&(d(e,\"ulp-validator-error\"),l(e,\"data-is-error\")):s(e,\"ulp-validator-error\")||(f(e,\"ulp-validator-error\"),c(e,\"data-is-error\",!0));var i=o(r.parent,\".ulp-validator-error\");return p(r.parent,\"ulp-error\",!!i.length),a}function E(t){var n=y(t),e=(u(t,\"data-ulp-validation-event-listeners\")||\"\").replace(/\\s/g,\"\").split(\",\").filter(function(e){return!!e});e.length&&e.forEach(function(e){a(n.element,e,function(){x(t,b,e)})})}function A(e,t,n){b=!0;var r=n.filter(function(e){return!x(e,b,\"submit\")});if(!r.length)return t.submitter&&\"default\"==u(t.submitter,\"value\")&&c(t.submitter,\"disabled\",!0),void e.submit();m(t);var a=y(r[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:\"smooth\"})}function C(){var t=r('form[data-form-primary=\"true\"]'),n=_(t);0!==n.length&&(n.forEach(function(e){E(e)}),a(t,\"submit\",function(e){A(t,e,n)}))}function S(){if(n)for(var e in n)n.hasOwnProperty(e)&&(v[e]=n[e])}function F(){var e=r(\"form[data-disable-html-validations]\");e&&(S(),c(e,\"novalidate\",\"\"),C())}}}.exports(i.querySelector,i.querySelectorAll,i.addEventListener,i.getParent,i.getAttribute,i.setAttribute,i.removeAttribute,i.hasClass,i.addClass,i.removeClass,i.toggleClass,i.globalWindow,i.consoleWarn,i.preventFormSubmit,i.SUPPORTED_INPUT_TYPES,i.ELEMENT_TYPE_SELECTOR,o,a),{exports:function(r,o,a,i,u,c,l,t,s,f,e,n){if(!n.enable_ulp_wcag_compliance){var d=!1,p=e+',input[type=\"checkbox\"]';return y(),[p,v,m,h,g,b,w,y]}function v(e){var t=u(e,\"data-ulp-validation-function\"),n=i(e);return{functionName:t,element:r(n,p),parent:n}}function m(e){var a=[],i=[];return o(e,\"[data-ulp-validation-function]\").forEach(function(e){var t=v(e),n=[];if(t.element){if(\"input\"===t.element.tagName.toLowerCase()){var r=u(t.element,\"type\");\"checkbox\"!==r&&-1===f.indexOf(r)&&n.push(\"Unsupported input type: \"+r)}}else n.push(\"Could not find element\");l[t.functionName]||n.push(\"Could not find function with name: \"+t.functionName),n.length?i=i.concat(n):a.push(e)}),i.length&&t(i.join(\"\\r\\n\")),a}function h(e,t,n){var r=v(e),a=(0,l[r.functionName])(r.element,t,n);c(e,\"ulp-validator-error\",!a);var i=o(r.parent,\".ulp-validator-error\");return c(r.parent,\"ulp-error\",!!i.length),a}function g(t){var n=v(t),e=(u(t,\"data-ulp-validation-event-listeners\")||\"\").replace(/\\s/g,\"\").split(\",\").filter(function(e){return!!e});e.length&&e.forEach(function(e){a(n.element,e,function(){h(t,d,e)})})}function b(e,t,n){d=!0;var r=n.filter(function(e){return!h(e,d,\"submit\")});if(r.length){s(t);var a=v(r[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:\"smooth\"})}else e.submit()}function w(){var t=r('form[data-form-primary=\"true\"]'),n=m(t);0!==n.length&&(n.forEach(function(e){g(e)}),a(t,\"submit\",function(e){b(t,e,n)}))}function y(){var e=o(\"[id^='ulp-container-']\");e&&e.length&&w()}}}.exports(i.querySelector,i.querySelectorAll,i.addEventListener,i.getParent,i.getAttribute,i.toggleClass,i.globalWindow,i.consoleWarn,i.preventFormSubmit,i.SUPPORTED_INPUT_TYPES,i.ELEMENT_TYPE_SELECTOR,a),{exports:function(e,t,n){function r(n){t(n,\"click\",function(e){e.preventDefault();var t=document.createElement(\"input\");t.name=\"action\",t.type=\"hidden\",t.value=n.value,n.form.appendChild(t),n.form.submit(),n.form.removeChild(t)})}function a(){e('form button[type=\"submit\"][formnovalidate]').forEach(function(e){r(e)})}return n&&a(),[a,r]}}.exports(i.querySelectorAll,i.addEventListener,i.RUN_INIT),((r={}).exports=function(o,e,u,c,l,s,t,f,n){if(n.enable_ulp_wcag_compliance){var r=e('[class*=\"aria-error-check\"]');if(r&&r.length){var a=t(function(e){e&&e.length&&e.map(function(e){if(e.target&&u(e.target,\"aria-error-check\")){var t=o('[id=\"'+c(e.target,\"data-ulp-validation-target\")+'\"');if(t){var n=c(t,\"aria-describedby\");c(e.target,\"data-is-error\")?(r=t,a=n,i=e.target.id,a&&-1!==a.search(i)||l(r,\"aria-describedby\",a?a+\" \"+i:i),l(r,\"aria-invalid\",!0)):function(e,t,n){if(t){var r=f(t,n);r.length?l(e,\"aria-describedby\",r):(s(e,\"aria-invalid\"),s(e,\"aria-describedby\"))}else s(e,\"aria-invalid\"),s(e,\"aria-describedby\")}(t,n,e.target.id)}}var r,a,i})});a&&r.map(function(e){a.observe(e,{attributes:!0,attributeFilter:[\"class\",\"data-is-error\"]})})}}},r.exports)(i.querySelector,i.querySelectorAll,i.hasClass,i.getAttribute,i.setAttribute,i.removeAttribute,i.createMutationObserver,i.removeAndTrimString,a)}();\n</script>\n</div>\n  \n\n</body></html>", "localStorage": "{\"stonlyWidget_autolaunchTriggered_81121\":\"{\\\"value\\\":1,\\\"ttl\\\":1785350487566}\",\"user-type-free-or-paid|a63a90da-025c-4194-8869-3dded85ef40b\":\"Paid\",\"stonlyWidget_language\":\"{\\\"value\\\":\\\"en\\\",\\\"ttl\\\":1785350488585}\",\"feature-experiment|increase_truck_posting_volume|a63a90da-025c-4194-8869-3dded85ef40b\":\"\\\"user-is-not-in-experiment\\\"\",\"optimizely-vuid\":\"vuid_e1030b380f774b47aa0ddd5b8b6\",\"profile\":\"{\\\"sub\\\":\\\"auth0|64355ce3d8f24d23556f11f9\\\",\\\"given_name\\\":\\\"Hakob\\\",\\\"family_name\\\":\\\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\\\",\\\"nickname\\\":\\\"info\\\",\\\"name\\\":\\\"<PERSON><PERSON><PERSON>\\\",\\\"picture\\\":\\\"https://s.gravatar.com/avatar/87c7123a692212406838ffcff231f79a?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fin.png\\\",\\\"updated_at\\\":\\\"2025-06-29T18:40:46.255Z\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"email_verified\\\":true,\\\"https://dat.com/accountname\\\":\\\"Reize LLC\\\",\\\"https://dat.com/csb_id\\\":3191151,\\\"https://dat.com/user_id\\\":\\\"a63a90da-025c-4194-8869-3dded85ef40b\\\",\\\"https://dat.com/office_id\\\":\\\"a63a90da-025c-4194-8869-3dded85ef40b\\\"}\",\"_hjUserAttributes\":\"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"stonly_anonymous_id\":\"e8e2e61c-c3c9-4a93-b17e-dfed0187e956\",\"ajs_anonymous_id\":\"\\\"2d802767-bbc7-4e1d-8278-************\\\"\",\"ajs_user_id\":\"\\\"auth0|64355ce3d8f24d23556f11f9\\\"\",\"ajs_user_traits\":\"{\\\"email\\\":\\\"<EMAIL>\\\",\\\"application_mode\\\":\\\"Paid\\\",\\\"name\\\":\\\"Hakob Ter-Sahakyan\\\",\\\"cp_\\\":{\\\"cp_freight:loadsearches:manage\\\":true,\\\"cp_visibility:shipmentrequestresponse:create\\\":true,\\\"cp_visibility:assignedshipments:read\\\":true,\\\"cp_visibility:shipmenttrackingrecord:create\\\":true,\\\"cp_freight:privatenetworkquerymatches:read\\\":true,\\\"cp_freight:privatenetworkqueryloadmatches:read\\\":true,\\\"cp_freight_matching_create_shipment_alarms\\\":true,\\\"cp_freight_matching_convert_to_search\\\":true,\\\"cp_freight_matching_search_shipments\\\":true,\\\"cp_freight_matching_search_enhancement_expanded\\\":true,\\\"cp_freight_matching_search_enhancement_active\\\":true,\\\"cp_freight_matching_lookup_posting\\\":true,\\\"cp_freight_matching_update_postings\\\":true,\\\"cp_freight_matching_create_equipment\\\":true,\\\"cp_freight_matching_lookup_searches\\\":true,\\\"cp_freight_matching_update_searches\\\":true,\\\"cp_freight_matching_create_alarms\\\":true,\\\"cp_freight_matching_lookup_alarms\\\":true,\\\"cp_freight_matching_update_alarms\\\":true,\\\"cp_freightmatching_view_equipment\\\":true,\\\"cp_directory_lookup_partial_credit_profile\\\":true,\\\"cp_directory_lookup_credit_score\\\":true,\\\"cp_directory_lookup_credit_report\\\":true,\\\"cp_directory_lookup_partial_self_profile\\\":true,\\\"cp_directory_lookup_full_self_profile\\\":true,\\\"cp_geography_lookup_truckstops\\\":true,\\\"cp_geography_lookup_postal_codes\\\":true,\\\"cp_freight_matching_include_credit_score\\\":true,\\\"cp_freight_matching_include_days_to_pay\\\":true,\\\"cp_freight_matching_cancel_posting\\\":true,\\\"cp_truck_miles\\\":true,\\\"cp_factoring_link\\\":true,\\\"cp_enable_routing_lookup_a\\\":true,\\\"cp_enable_routing_lookup_b\\\":true,\\\"cp_reputation_read\\\":true,\\\"cp_reputation_write\\\":true,\\\"cp_reputation_comment\\\":true,\\\"cp_spot_number_of_days\\\":true,\\\"cp_spot_geography\\\":true,\\\"cp_spot_rate_single_lookup_lite\\\":true,\\\"cp_allow_voice_support\\\":true,\\\"cp_spot_rate_best_expansion_ma_30\\\":true,\\\"cp_freight_matching_enable_view_private_loads\\\":true,\\\"cp_freight:runloadsearcheslow:enable\\\":true},\\\"calculated_segment\\\":\\\"carrier\\\",\\\"application_version\\\":\\\"1.0\\\",\\\"dat_primary_application\\\":\\\"DAT One Web\\\",\\\"tracking_version\\\":\\\"0.2\\\"}\",\"national-assets-count-view-type|a63a90da-025c-4194-8869-3dded85ef40b\":\"SHIPMENT\"}", "sessionStorage": "{\"ask\":\"\\\"/dashboard\\\"\",\"lsk\":\"\\\"eyJlbWFpbEFkZHJlc3MiOiIiLCJsb2dpblJ1bnRpbWVPdmVycmlkZXMiOnt9fQ==\\\"\"}", "cookies": "dat_agent_device_id=********-10f9-d3e6-9bb5-9cc19c702ce4; ajs_anonymous_id=2d802767-bbc7-4e1d-8278-************; _hjSession_2420462=eyJpZCI6ImQwNmY5NzY3LWZhMTgtNDI1Yy1hOTBmLTE2MmM1ZTg2MGFiOSIsImMiOjE3NTEyMjI0Mzc2OTAsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; ajs_user_id=auth0|64355ce3d8f24d23556f11f9; _hjSessionUser_2420462=eyJpZCI6IjVjYTk1Yjg2LWI3ZjEtNTMyYS04OGM2LTQxYjkyZjY0ZTk4NyIsImNyZWF0ZWQiOjE3NTEyMjI0Mzc2ODgsImV4aXN0aW5nIjp0cnVlfQ==", "timestamp": "2025-06-29T19:11:31.938Z", "cookiesArray": [{"name": "auth0", "value": "s%3Av1.gadzZXNzaW9ugqZoYW5kbGXEQE-80Qx_wljzIQVPh5MnjUzszun_LzXQPKDoMfsC6h_ABpSIMVNRNXSo6rM-ybRA-wvw06KnL1ToaCGKnqhIGIqmY29va2llg6dleHBpcmVz1__GIDcAaGWERa5vcmlnaW5hbE1heEFnZc4PcxQAqHNhbWVTaXRlpG5vbmU.hNgoDZQuwniagDSZLShqsdeaPA9%2FlsUFoX0if9mnFrE", "domain": "login.dat.com", "path": "/", "expires": 1751483461.867389, "size": 249, "httpOnly": true, "secure": true, "session": false, "sameSite": "None", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "did", "value": "s%3Av0%3Ac7eb916a-3ff9-4038-a551-5844ee7a6caa.ZcjF%2BkhJCZ0C9bsxffjsXm0XMsj9j0WvJvWybVNkNpw", "domain": "login.dat.com", "path": "/", "expires": 1782781861.661617, "size": 94, "httpOnly": true, "secure": true, "session": false, "sameSite": "None", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "_hjSessionUser_2420462", "value": "eyJpZCI6IjVjYTk1Yjg2LWI3ZjEtNTMyYS04OGM2LTQxYjkyZjY0ZTk4NyIsImNyZWF0ZWQiOjE3NTEyMjI0Mzc2ODgsImV4aXN0aW5nIjp0cnVlfQ==", "domain": ".dat.com", "path": "/", "expires": 1782760261, "size": 138, "httpOnly": false, "secure": true, "session": false, "sameSite": "None", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "ajs_user_id", "value": "auth0|64355ce3d8f24d23556f11f9", "domain": ".dat.com", "path": "/", "expires": 1782760052, "size": 41, "httpOnly": false, "secure": false, "session": false, "sameSite": "Lax", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "_hjSession_2420462", "value": "eyJpZCI6ImQwNmY5NzY3LWZhMTgtNDI1Yy1hOTBmLTE2MmM1ZTg2MGFiOSIsImMiOjE3NTEyMjI0Mzc2OTAsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=", "domain": ".dat.com", "path": "/", "expires": 1751224570, "size": 166, "httpOnly": false, "secure": true, "session": false, "sameSite": "None", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "ajs_anonymous_id", "value": "2d802767-bbc7-4e1d-8278-************", "domain": ".dat.com", "path": "/", "expires": 1782760052, "size": 52, "httpOnly": false, "secure": false, "session": false, "sameSite": "Lax", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "did_compat", "value": "s%3Av0%3Ac7eb916a-3ff9-4038-a551-5844ee7a6caa.ZcjF%2BkhJCZ0C9bsxffjsXm0XMsj9j0WvJvWybVNkNpw", "domain": "login.dat.com", "path": "/", "expires": 1782781861.661967, "size": 101, "httpOnly": true, "secure": true, "session": false, "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "dat_agent_device_id", "value": "********-10f9-d3e6-9bb5-9cc19c702ce4", "domain": ".dat.com", "path": "/", "expires": 1785782472.812857, "size": 55, "httpOnly": false, "secure": false, "session": false, "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "auth0_compat", "value": "s%3Av1.gadzZXNzaW9ugqZoYW5kbGXEQE-80Qx_wljzIQVPh5MnjUzszun_LzXQPKDoMfsC6h_ABpSIMVNRNXSo6rM-ybRA-wvw06KnL1ToaCGKnqhIGIqmY29va2llg6dleHBpcmVz1__GIDcAaGWERa5vcmlnaW5hbE1heEFnZc4PcxQAqHNhbWVTaXRlpG5vbmU.hNgoDZQuwniagDSZLShqsdeaPA9%2FlsUFoX0if9mnFrE", "domain": "login.dat.com", "path": "/", "expires": 1751483461.868112, "size": 256, "httpOnly": true, "secure": true, "session": false, "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}, {"name": "__cf_bm", "value": "uIjtRWfS9DNaLCurBo49IjXNU7Gj3NG9R4GpsBeFqqQ-1751223451-1.0.1.1-Py8EHymf2J0KYp.9Jq2du6G1j1ehs3_QRNBRVYmzBfAj6dbw8m3VAOck6no0Fe5xp6yuWynrF2il7nH.LUfBxhXqf6n20VhB.FN6fSgdyoQ", "domain": ".dat.com", "path": "/", "expires": 1751225251.672281, "size": 177, "httpOnly": true, "secure": true, "session": false, "sameSite": "None", "priority": "Medium", "sameParty": false, "sourceScheme": "Secure"}]}