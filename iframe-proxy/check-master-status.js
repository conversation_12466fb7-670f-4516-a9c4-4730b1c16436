const fs = require('fs');
const puppeteer = require('puppeteer');

async function checkMasterSessionStatus() {
    console.log('🔍 Checking master session status...');
    
    try {
        // Check if master session data exists
        if (!fs.existsSync('master-session-data.json')) {
            console.log('❌ No master session data found');
            return { status: 'not_found', needsStart: true };
        }
        
        // Load master session data
        const sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
        console.log('✅ Master session data found');
        console.log(`📍 Last URL: ${sessionData.currentUrl}`);
        console.log(`⏰ Last Updated: ${sessionData.timestamp}`);
        
        // Check if session data is recent (within last 10 minutes)
        const lastUpdate = new Date(sessionData.timestamp);
        const now = new Date();
        const minutesOld = (now - lastUpdate) / (1000 * 60);
        
        console.log(`🕐 Session data is ${minutesOld.toFixed(1)} minutes old`);
        
        if (minutesOld > 10) {
            console.log('⚠️ Session data is stale (>10 minutes old)');
            return { status: 'stale', needsStart: true, sessionData };
        }
        
        // Test if session is still valid by making a quick browser check
        console.log('🧪 Testing session validity...');
        
        const browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage'
            ]
        });
        
        const page = await browser.newPage();
        
        try {
            // Set cookies and storage from session data
            if (sessionData.cookies && Array.isArray(sessionData.cookies)) {
                await page.setCookie(...sessionData.cookies);
            }
            
            if (sessionData.localStorage) {
                await page.evaluateOnNewDocument((localStorageData) => {
                    const localStorage = JSON.parse(localStorageData);
                    for (const [key, value] of Object.entries(localStorage)) {
                        window.localStorage.setItem(key, value);
                    }
                }, sessionData.localStorage);
            }
            
            // Navigate to DAT dashboard
            await page.goto('https://one.dat.com/dashboard', { 
                waitUntil: 'networkidle2',
                timeout: 15000 
            });
            
            // Wait a moment for page to load
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const currentUrl = page.url();
            const title = await page.title();
            
            console.log(`📍 Test URL: ${currentUrl}`);
            console.log(`📄 Test Title: ${title}`);
            
            // Check if we're authenticated (not redirected to login)
            if (currentUrl.includes('login') || currentUrl.includes('auth')) {
                console.log('❌ Session expired - redirected to login');
                await browser.close();
                return { status: 'expired', needsStart: true, sessionData };
            }
            
            // Check if we're on dashboard
            if (currentUrl.includes('dashboard') || title.toLowerCase().includes('dat')) {
                console.log('✅ Session is valid and authenticated!');
                await browser.close();
                return { status: 'valid', needsStart: false, sessionData };
            }
            
            console.log('⚠️ Session state unclear');
            await browser.close();
            return { status: 'unclear', needsStart: true, sessionData };
            
        } catch (error) {
            console.log(`❌ Session test failed: ${error.message}`);
            await browser.close();
            return { status: 'test_failed', needsStart: true, sessionData };
        }
        
    } catch (error) {
        console.error('❌ Error checking master session:', error.message);
        return { status: 'error', needsStart: true };
    }
}

async function checkLiveBrowserServer() {
    console.log('🌐 Checking if live browser server is running...');
    
    try {
        const fetch = require('node-fetch');
        const response = await fetch('http://localhost:3005/api/browser-state', { timeout: 5000 });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Live browser server is running');
            console.log(`📍 Current URL: ${data.url}`);
            console.log(`🔐 Authenticated: ${data.isAuthenticated}`);
            return { running: true, data };
        } else {
            console.log('❌ Live browser server not responding');
            return { running: false };
        }
    } catch (error) {
        console.log('❌ Live browser server not running');
        return { running: false };
    }
}

async function main() {
    console.log('🔍 MASTER SESSION STATUS CHECK');
    console.log('================================');
    
    const sessionStatus = await checkMasterSessionStatus();
    const serverStatus = await checkLiveBrowserServer();
    
    console.log('\n📊 STATUS SUMMARY:');
    console.log(`🔐 Master Session: ${sessionStatus.status}`);
    console.log(`🌐 Live Browser Server: ${serverStatus.running ? 'Running' : 'Not Running'}`);
    
    if (sessionStatus.status === 'valid' && serverStatus.running) {
        console.log('\n🎉 EVERYTHING IS WORKING!');
        console.log('🔴 Live Master Browser: http://147.93.146.10:3005/live-browser-iframe');
        console.log('📡 Direct HTML: http://147.93.146.10:3005/live-browser');
        console.log('📊 Browser State API: http://147.93.146.10:3005/api/browser-state');
    } else {
        console.log('\n⚠️ ISSUES DETECTED:');
        
        if (sessionStatus.needsStart) {
            console.log('❌ Master session needs to be started/restarted');
            console.log('💡 Run: node start-master-session.js');
        }
        
        if (!serverStatus.running) {
            console.log('❌ Live browser server is not running');
            console.log('💡 This will start automatically with master session');
        }
    }
    
    return {
        sessionStatus,
        serverStatus,
        allGood: sessionStatus.status === 'valid' && serverStatus.running
    };
}

if (require.main === module) {
    main()
        .then(result => {
            if (result.allGood) {
                console.log('\n✅ All systems operational!');
                process.exit(0);
            } else {
                console.log('\n⚠️ Some issues need attention');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n❌ Status check failed:', error.message);
            process.exit(1);
        });
}

module.exports = { checkMasterSessionStatus, checkLiveBrowserServer, main };
