const express = require('express');
const http = require('http');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class NoVNCBrowserServer {
    constructor() {
        this.app = express();
        this.server = null;
        this.vncServer = null;
        this.xvfbProcess = null;
        this.chromeProcess = null;
        this.websockifyProcess = null;
        this.display = ':99';
        this.vncPort = 5900;
        this.websockifyPort = 6080;
    }

    async setupVirtualDisplay() {
        console.log('🖥️ Setting up virtual display...');
        
        return new Promise((resolve, reject) => {
            // Start Xvfb (X Virtual Framebuffer)
            this.xvfbProcess = spawn('Xvfb', [
                this.display,
                '-screen', '0', '1920x1080x24',
                '-ac',
                '+extension', 'GLX',
                '+render',
                '-noreset'
            ], {
                stdio: 'pipe'
            });

            this.xvfbProcess.on('error', (error) => {
                console.error('❌ Failed to start Xvfb:', error);
                reject(error);
            });

            // Give Xvfb time to start
            setTimeout(() => {
                console.log('✅ Virtual display started on', this.display);
                resolve();
            }, 2000);
        });
    }

    async startVNCServer() {
        console.log('🔌 Starting VNC server...');
        
        return new Promise((resolve, reject) => {
            // Start x11vnc server
            this.vncServer = spawn('x11vnc', [
                '-display', this.display,
                '-nopw',
                '-listen', '0.0.0.0',
                '-xkb',
                '-ncache', '10',
                '-ncache_cr',
                '-rfbport', this.vncPort.toString(),
                '-forever',
                '-shared'
            ], {
                stdio: 'pipe'
            });

            this.vncServer.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('VNC:', output.trim());
                
                if (output.includes('listening on port')) {
                    console.log('✅ VNC server started on port', this.vncPort);
                    resolve();
                }
            });

            this.vncServer.on('error', (error) => {
                console.error('❌ Failed to start VNC server:', error);
                reject(error);
            });
        });
    }

    async startChrome() {
        console.log('🌐 Starting Chrome browser...');
        
        return new Promise((resolve, reject) => {
            // Start Chrome in the virtual display
            this.chromeProcess = spawn('chromium', [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--start-maximized',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-infobars',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--user-data-dir=/tmp/chrome-novnc',
                'https://one.dat.com/dashboard'
            ], {
                env: {
                    ...process.env,
                    DISPLAY: this.display
                },
                stdio: 'pipe'
            });

            this.chromeProcess.on('error', (error) => {
                console.error('❌ Failed to start Chrome:', error);
                reject(error);
            });

            // Give Chrome time to start
            setTimeout(() => {
                console.log('✅ Chrome browser started');
                resolve();
            }, 3000);
        });
    }

    async startWebsockify() {
        console.log('🌐 Starting websockify bridge...');

        return new Promise((resolve, reject) => {
            // Start websockify to bridge WebSocket to VNC
            this.websockifyProcess = spawn('websockify', [
                '--web=/usr/share/novnc',
                this.websockifyPort.toString(),
                `localhost:${this.vncPort}`
            ], {
                stdio: 'pipe'
            });

            this.websockifyProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('Websockify:', output.trim());

                if (output.includes('listening on')) {
                    console.log('✅ Websockify started on port', this.websockifyPort);
                    resolve();
                }
            });

            this.websockifyProcess.on('error', (error) => {
                console.error('❌ Failed to start websockify:', error);
                reject(error);
            });
        });
    }

    setupRoutes() {
        // Serve noVNC client
        this.app.get('/', (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - noVNC Remote Browser</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: #1a365d; 
            overflow: hidden;
            height: 100vh;
        }
        .header {
            background: #2d3748; 
            color: white; 
            padding: 10px 20px;
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            position: fixed; 
            top: 0; 
            left: 0; 
            right: 0; 
            z-index: 1000;
            height: 50px;
        }
        .vnc-container {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .connected { background: #48bb78; }
        .connecting { background: #ed8936; }
        .disconnected { background: #f56565; }
        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .btn:hover { background: #3182ce; }
        #noVNC_canvas {
            width: 100% !important;
            height: 100% !important;
        }
    </style>
    <script src="https://unpkg.com/@novnc/novnc@1.4.0/lib/rfb.js"></script>
</head>
<body>
    <div class="header">
        <h1>🚛 DAT Load Board - Remote Browser (noVNC)</h1>
        <div>
            <button class="btn" onclick="connectVNC()">🔌 Connect</button>
            <button class="btn" onclick="disconnectVNC()">❌ Disconnect</button>
            <button class="btn" onclick="fullscreen()">⛶ Fullscreen</button>
            <div class="status connecting" id="status">🟡 Connecting...</div>
        </div>
    </div>
    
    <div class="vnc-container" id="vnc-container">
        <div id="noVNC_canvas_container">
            <canvas id="noVNC_canvas"></canvas>
        </div>
    </div>

    <script>
        let rfb = null;
        
        function connectVNC() {
            const host = window.location.hostname;
            const port = ${this.websockifyPort};
            const url = 'ws://' + host + ':' + port + '/';

            console.log('🔌 Connecting to VNC server:', url);

            rfb = new NoVNC.RFB(document.getElementById('noVNC_canvas'), url, {
                credentials: { password: '' }
            });
            
            rfb.addEventListener('connect', () => {
                console.log('✅ Connected to VNC server');
                updateStatus('connected', '🟢 LIVE');
            });
            
            rfb.addEventListener('disconnect', () => {
                console.log('❌ Disconnected from VNC server');
                updateStatus('disconnected', '🔴 Disconnected');
            });
            
            rfb.addEventListener('credentialsrequired', () => {
                console.log('🔐 Credentials required');
                updateStatus('connecting', '🔐 Auth Required');
            });
        }
        
        function disconnectVNC() {
            if (rfb) {
                rfb.disconnect();
                rfb = null;
            }
        }
        
        function fullscreen() {
            const container = document.getElementById('vnc-container');
            if (container.requestFullscreen) {
                container.requestFullscreen();
            }
        }
        
        function updateStatus(type, text) {
            const status = document.getElementById('status');
            status.className = 'status ' + type;
            status.textContent = text;
        }
        
        // Auto-connect on load
        window.addEventListener('load', () => {
            setTimeout(connectVNC, 1000);
        });
    </script>
</body>
</html>`;
            res.send(html);
        });

        // Health check
        this.app.get('/status', (req, res) => {
            res.json({
                vncServer: this.vncServer ? 'running' : 'stopped',
                chrome: this.chromeProcess ? 'running' : 'stopped',
                display: this.display,
                vncPort: this.vncPort,
                websockifyPort: this.websockifyPort
            });
        });
    }

    async start(port = 3007) {
        try {
            console.log('🚀 Starting noVNC Browser Server...');
            
            // Setup virtual display
            await this.setupVirtualDisplay();
            
            // Start VNC server
            await this.startVNCServer();

            // Start websockify bridge
            await this.startWebsockify();

            // Start Chrome browser
            await this.startChrome();
            
            // Setup web routes
            this.setupRoutes();
            
            // Start web server
            this.server = http.createServer(this.app);
            this.server.listen(port, '0.0.0.0', () => {
                console.log(`🌐 noVNC Browser Server running on port ${port}`);
                console.log(`🖥️ Remote DAT Browser: http://147.93.146.10:${port}/`);
                console.log(`📊 Status API: http://147.93.146.10:${port}/status`);
                console.log(`🔌 VNC Server: ${this.vncPort}`);
            });
            
        } catch (error) {
            console.error('❌ Failed to start noVNC browser server:', error);
            process.exit(1);
        }
    }

    async stop() {
        console.log('🛑 Stopping noVNC Browser Server...');
        
        if (this.chromeProcess) {
            this.chromeProcess.kill();
        }
        
        if (this.websockifyProcess) {
            this.websockifyProcess.kill();
        }

        if (this.vncServer) {
            this.vncServer.kill();
        }

        if (this.xvfbProcess) {
            this.xvfbProcess.kill();
        }
        
        if (this.server) {
            this.server.close();
        }
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    if (global.noVNCServer) {
        await global.noVNCServer.stop();
    }
    process.exit(0);
});

// Start the server
const server = new NoVNCBrowserServer();
global.noVNCServer = server;
server.start().catch(console.error);

module.exports = NoVNCBrowserServer;
