const { spawn, execSync } = require('child_process');
const puppeteer = require('puppeteer');
const fs = require('fs');

class VNCMasterSession {
    constructor() {
        this.xvfbProcess = null;
        this.vncProcess = null;
        this.websockifyProcess = null;
        this.masterBrowser = null;
        this.masterPage = null;
        this.isAuthenticated = false;
        this.display = ':99';
        this.vncPort = 5900;
        this.websockifyPort = 6080;
    }

    async startVirtualDisplay() {
        console.log('🖥️ Starting virtual display...');

        // Kill any existing Chrome processes first
        try {
            execSync('pkill -f chrome', { stdio: 'ignore' });
            await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (e) {
            // Ignore if no processes to kill
        }

        return new Promise((resolve, reject) => {
            this.xvfbProcess = spawn('Xvfb', [
                this.display,
                '-screen', '0', '1920x1080x24',
                '-ac',
                '+extension', 'GLX',
                '+render',
                '-noreset'
            ], {
                stdio: 'pipe'
            });

            this.xvfbProcess.on('error', (error) => {
                console.error('❌ Failed to start Xvfb:', error);
                reject(error);
            });

            setTimeout(() => {
                console.log('✅ Virtual display started on', this.display);
                resolve();
            }, 3000);
        });
    }

    async startVNCServer() {
        console.log('🔌 Starting VNC server...');
        
        return new Promise((resolve, reject) => {
            this.vncProcess = spawn('x11vnc', [
                '-display', this.display,
                '-nopw',
                '-listen', '0.0.0.0',
                '-xkb',
                '-ncache', '10',
                '-ncache_cr',
                '-rfbport', this.vncPort.toString(),
                '-forever',
                '-shared'
            ], {
                stdio: 'pipe'
            });

            this.vncProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('listening on port')) {
                    console.log('✅ VNC server started on port', this.vncPort);
                    resolve();
                }
            });

            this.vncProcess.stderr.on('data', (data) => {
                console.log('VNC:', data.toString().trim());
            });

            this.vncProcess.on('error', (error) => {
                console.error('❌ Failed to start VNC server:', error);
                reject(error);
            });

            setTimeout(() => {
                console.log('✅ VNC server should be running');
                resolve();
            }, 10000);
        });
    }

    async startWebsockify() {
        console.log('🌐 Starting websockify bridge...');
        
        return new Promise((resolve, reject) => {
            this.websockifyProcess = spawn('websockify', [
                '--web=/usr/share/novnc',
                this.websockifyPort.toString(),
                `localhost:${this.vncPort}`
            ], {
                stdio: 'pipe'
            });

            this.websockifyProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('listening on')) {
                    console.log('✅ Websockify started on port', this.websockifyPort);
                    resolve();
                }
            });

            this.websockifyProcess.stderr.on('data', (data) => {
                console.log('Websockify:', data.toString().trim());
            });

            this.websockifyProcess.on('error', (error) => {
                console.error('❌ Failed to start websockify:', error);
                reject(error);
            });

            setTimeout(() => {
                console.log('✅ Websockify should be running');
                resolve();
            }, 10000);
        });
    }

    async startMasterBrowser() {
        console.log('🚀 Starting visual master browser...');
        
        // Set DISPLAY environment variable
        process.env.DISPLAY = this.display;
        
        this.masterBrowser = await puppeteer.launch({
            headless: false,  // Run in visual mode!
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--start-maximized',
                '--window-size=1920,1080',
                '--window-position=0,0',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-infobars',
                '--disable-extensions',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-session-crashed-bubble',
                '--disable-restore-session-state',
                '--disable-background-networking'
            ],
            env: {
                ...process.env,
                DISPLAY: this.display
            }
        });

        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1920, height: 1080 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        console.log('✅ Visual master browser started');
        return this.masterPage;
    }

    async authenticateAndNavigate() {
        console.log('🔐 Navigating to DAT and authenticating...');
        
        // Load existing session data if available
        let sessionData = null;
        if (fs.existsSync('master-session-data.json')) {
            sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
            console.log('📄 Found existing session data');
        }

        // Navigate to DAT
        await this.masterPage.goto('https://one.dat.com/dashboard', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });

        // If we have session data, try to restore it
        if (sessionData && sessionData.cookies) {
            console.log('🍪 Restoring session cookies...');
            await this.masterPage.setCookie(...sessionData.cookies);
            await this.masterPage.reload({ waitUntil: 'networkidle2' });
        }

        // Check if already authenticated
        await new Promise(resolve => setTimeout(resolve, 5000));
        const currentUrl = this.masterPage.url();
        
        if (currentUrl.includes('one.dat.com') && !currentUrl.includes('login')) {
            console.log('✅ Already authenticated!');
            this.isAuthenticated = true;
        } else {
            console.log('🔐 Need to authenticate - please use the VNC interface');
            console.log('📍 Navigate to: http://147.93.146.10:6080/vnc.html');
            console.log('🖱️ You can interact with the browser directly through VNC');
        }

        return this.isAuthenticated;
    }

    async start() {
        try {
            console.log('🚀 Starting VNC Master Session...');
            
            // Start virtual display
            await this.startVirtualDisplay();
            
            // Start VNC server
            await this.startVNCServer();
            
            // Start websockify bridge
            await this.startWebsockify();
            
            // Start visual master browser
            await this.startMasterBrowser();
            
            // Navigate and authenticate
            await this.authenticateAndNavigate();
            
            console.log('🎉 VNC Master Session ready!');
            console.log('🖥️ VNC Web Interface: http://147.93.146.10:6080/vnc.html');
            console.log('🔗 Direct noVNC: http://147.93.146.10:6080/vnc.html?autoconnect=true&resize=scale');
            console.log('📺 You can now see and control the DAT browser through VNC!');
            
            // Keep the process alive
            process.on('SIGINT', () => this.stop());
            
        } catch (error) {
            console.error('❌ Failed to start VNC master session:', error);
            await this.stop();
            process.exit(1);
        }
    }

    async stop() {
        console.log('🛑 Stopping VNC Master Session...');
        
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        
        if (this.websockifyProcess) {
            this.websockifyProcess.kill();
        }
        
        if (this.vncProcess) {
            this.vncProcess.kill();
        }
        
        if (this.xvfbProcess) {
            this.xvfbProcess.kill();
        }
    }
}

// Start the VNC master session
const vncMaster = new VNCMasterSession();
vncMaster.start().catch(console.error);

module.exports = VNCMasterSession;
