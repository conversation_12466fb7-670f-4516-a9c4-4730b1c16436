const express = require('express');
const http = require('http');
const { spawn } = require('child_process');

class SimpleVNCServer {
    constructor() {
        this.app = express();
        this.server = null;
        this.vncProcess = null;
        this.websockifyProcess = null;
        this.display = ':99';
        this.vncPort = 5900;
        this.websockifyPort = 6080;
    }

    async startVNCServer() {
        console.log('🔌 Starting simple VNC server...');
        
        return new Promise((resolve, reject) => {
            // Start x11vnc on the existing display (where master browser runs)
            this.vncProcess = spawn('x11vnc', [
                '-display', ':0',  // Use the main display instead of virtual
                '-nopw',
                '-listen', '0.0.0.0',
                '-xkb',
                '-ncache', '10',
                '-ncache_cr',
                '-rfbport', this.vncPort.toString(),
                '-forever',
                '-shared',
                '-viewonly'  // Make it view-only for safety
            ], {
                stdio: 'pipe'
            });

            this.vncProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('VNC:', output.trim());
                
                if (output.includes('listening on port')) {
                    console.log('✅ VNC server started on port', this.vncPort);
                    resolve();
                }
            });

            this.vncProcess.stderr.on('data', (data) => {
                console.log('VNC stderr:', data.toString().trim());
            });

            this.vncProcess.on('error', (error) => {
                console.error('❌ Failed to start VNC server:', error);
                reject(error);
            });

            // Timeout after 10 seconds
            setTimeout(() => {
                if (this.vncProcess) {
                    console.log('✅ VNC server should be running (timeout reached)');
                    resolve();
                }
            }, 10000);
        });
    }

    async startWebsockify() {
        console.log('🌐 Starting websockify bridge...');
        
        return new Promise((resolve, reject) => {
            this.websockifyProcess = spawn('websockify', [
                '--web=/usr/share/novnc',
                this.websockifyPort.toString(),
                `localhost:${this.vncPort}`
            ], {
                stdio: 'pipe'
            });

            this.websockifyProcess.stdout.on('data', (data) => {
                const output = data.toString();
                console.log('Websockify:', output.trim());
                
                if (output.includes('listening on')) {
                    console.log('✅ Websockify started on port', this.websockifyPort);
                    resolve();
                }
            });

            this.websockifyProcess.stderr.on('data', (data) => {
                console.log('Websockify stderr:', data.toString().trim());
            });

            this.websockifyProcess.on('error', (error) => {
                console.error('❌ Failed to start websockify:', error);
                reject(error);
            });

            // Timeout after 10 seconds
            setTimeout(() => {
                if (this.websockifyProcess) {
                    console.log('✅ Websockify should be running (timeout reached)');
                    resolve();
                }
            }, 10000);
        });
    }

    setupRoutes() {
        // Serve simple VNC viewer
        this.app.get('/', (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - VNC Remote Desktop</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: #1a365d; 
            overflow: hidden;
            height: 100vh;
        }
        .header {
            background: #2d3748; 
            color: white; 
            padding: 10px 20px;
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            position: fixed; 
            top: 0; 
            left: 0; 
            right: 0; 
            z-index: 1000;
            height: 50px;
        }
        .vnc-container {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .connected { background: #48bb78; }
        .connecting { background: #ed8936; }
        .disconnected { background: #f56565; }
        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .btn:hover { background: #3182ce; }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 DAT Load Board - Remote Desktop (VNC)</h1>
        <div>
            <button class="btn" onclick="refreshVNC()">🔄 Refresh</button>
            <button class="btn" onclick="openFullscreen()">⛶ Fullscreen</button>
            <div class="status connected" id="status">🟢 LIVE</div>
        </div>
    </div>
    
    <div class="vnc-container" id="vnc-container">
        <iframe id="vnc-frame" src="http://*************:${this.websockifyPort}/vnc.html?autoconnect=true&resize=scale"></iframe>
    </div>

    <script>
        function refreshVNC() {
            const frame = document.getElementById('vnc-frame');
            frame.src = frame.src;
        }
        
        function openFullscreen() {
            const container = document.getElementById('vnc-container');
            if (container.requestFullscreen) {
                container.requestFullscreen();
            }
        }
        
        // Auto-refresh every 30 seconds to maintain connection
        setInterval(() => {
            console.log('🔄 Keeping VNC connection alive...');
        }, 30000);
    </script>
</body>
</html>`;
            res.send(html);
        });

        // Status endpoint
        this.app.get('/status', (req, res) => {
            res.json({
                vncServer: this.vncProcess ? 'running' : 'stopped',
                websockify: this.websockifyProcess ? 'running' : 'stopped',
                vncPort: this.vncPort,
                websockifyPort: this.websockifyPort,
                display: this.display
            });
        });
    }

    async start(port = 3008) {
        try {
            console.log('🚀 Starting Simple VNC Server...');
            
            // Start VNC server on main display
            await this.startVNCServer();
            
            // Start websockify bridge
            await this.startWebsockify();
            
            // Setup web routes
            this.setupRoutes();
            
            // Start web server
            this.server = http.createServer(this.app);
            this.server.listen(port, '0.0.0.0', () => {
                console.log(`🌐 Simple VNC Server running on port ${port}`);
                console.log(`🖥️ Remote Desktop: http://*************:${port}/`);
                console.log(`📊 Status API: http://*************:${port}/status`);
                console.log(`🔌 VNC Port: ${this.vncPort}`);
                console.log(`🌐 Websockify Port: ${this.websockifyPort}`);
            });
            
        } catch (error) {
            console.error('❌ Failed to start VNC server:', error);
            process.exit(1);
        }
    }

    async stop() {
        console.log('🛑 Stopping Simple VNC Server...');
        
        if (this.websockifyProcess) {
            this.websockifyProcess.kill();
        }
        
        if (this.vncProcess) {
            this.vncProcess.kill();
        }
        
        if (this.server) {
            this.server.close();
        }
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    if (global.vncServer) {
        await global.vncServer.stop();
    }
    process.exit(0);
});

// Start the server
const server = new SimpleVNCServer();
global.vncServer = server;
server.start().catch(console.error);

module.exports = SimpleVNCServer;
