const MasterSessionManager = require('./master-session-manager');

async function startMasterSession() {
    const masterSession = new MasterSessionManager();
    
    try {
        console.log('🚀 Starting DAT Master Session Manager...');
        
        // Initialize and authenticate
        const success = await masterSession.authenticateMaster('<EMAIL>', 'm6BKJVYzJDugC9!');
        
        if (success) {
            console.log('🎉 Master session is running and authenticated!');
            console.log('💓 Keep-alive is active - session will stay logged in 24/7');
            console.log('📊 Session data is being updated every 5 minutes');
            console.log('👥 Ready to clone sessions for multiple users');
            
            // Keep the process running
            console.log('\n🔄 Master session is now running in background...');
            console.log('Press Ctrl+C to stop the master session');
            
            // Handle graceful shutdown
            process.on('SIGINT', async () => {
                console.log('\n🛑 Shutting down master session...');
                await masterSession.cleanup();
                process.exit(0);
            });
            
            // Keep process alive
            setInterval(() => {
                console.log(`💓 Master session heartbeat - ${new Date().toISOString()}`);
            }, 10 * 60 * 1000); // Every 10 minutes
            
        } else {
            console.error('❌ Failed to authenticate master session');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ Master session error:', error.message);
        await masterSession.cleanup();
        process.exit(1);
    }
}

// Export for use in other modules
module.exports = { startMasterSession };

// Run if called directly
if (require.main === module) {
    startMasterSession();
}
