const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');
const SessionSnapshotManager = require('./session-snapshot-manager');
const fetch = require('node-fetch');

const app = express();
const PORT = 3004;

// Enable CORS for all routes
app.use(cors());

// Initialize Session Snapshot Manager
const snapshotManager = new SessionSnapshotManager();

// Store authenticated sessions
const authenticatedSessions = new Map();

// Direct authenticated DAT dashboard endpoint (uses master session)
app.get('/dat-authenticated', (req, res) => {
    const masterSessionId = loadMasterSession();

    if (masterSessionId) {
        const authenticatedUrl = `http://${req.get('host')}/authenticated/${masterSessionId}/dat-one/dashboard`;
        res.redirect(authenticatedUrl);
    } else {
        res.status(500).send('Master session not available - please start master session manager');
    }
});

// Iframe wrapper for DAT dashboard
app.get('/dat-iframe', (req, res) => {
    const masterSessionId = loadMasterSession();

    if (masterSessionId) {
        const iframeUrl = `http://${req.get('host')}/authenticated/${masterSessionId}/dat-one/dashboard`;

        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }

        .header {
            background: #1a365d;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 18px;
            margin: 0;
        }

        .status {
            font-size: 12px;
            background: #38a169;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .iframe-container {
            width: 100%;
            height: calc(100vh - 50px);
            border: none;
            overflow: hidden;
        }

        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 16px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 DAT Load Board - Authenticated Access</h1>
        <div class="status">✅ Connected</div>
    </div>

    <div class="iframe-container">
        <div class="loading" id="loading">Loading DAT dashboard...</div>
        <iframe
            id="dat-iframe"
            src="${iframeUrl}"
            style="display: none;"
            onload="document.getElementById('loading').style.display='none'; this.style.display='block';"
            onerror="document.getElementById('loading').innerHTML='❌ Failed to load DAT dashboard';"
        ></iframe>
    </div>

    <script>
        // Auto-refresh iframe every 30 minutes to maintain session
        setInterval(() => {
            console.log('🔄 Refreshing DAT session...');
            document.getElementById('dat-iframe').src = document.getElementById('dat-iframe').src;
        }, 30 * 60 * 1000);

        // Handle iframe communication
        window.addEventListener('message', (event) => {
            console.log('📨 Message from DAT iframe:', event.data);
        });
    </script>
</body>
</html>`;

        res.send(html);
    } else {
        res.status(500).send(`
<!DOCTYPE html>
<html>
<head><title>DAT Load Board - Error</title></head>
<body style="font-family: Arial; padding: 20px; text-align: center;">
    <h1>❌ Master Session Not Available</h1>
    <p>Please start the master session manager first.</p>
    <p><code>node start-master-session.js</code></p>
</body>
</html>`);
    }
});

// KASM-style Session Snapshotting Endpoints
app.post('/api/snapshot/initialize', async (req, res) => {
    try {
        console.log('🎯 Initializing session snapshot manager...');
        await snapshotManager.initializeMasterSession();
        await snapshotManager.createSessionSnapshot();

        res.json({
            success: true,
            message: 'Session snapshot manager initialized',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Failed to initialize snapshot manager:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/snapshot/create', async (req, res) => {
    try {
        console.log('📸 Creating new session snapshot...');
        const snapshot = await snapshotManager.createSessionSnapshot();

        res.json({
            success: true,
            snapshot: {
                url: snapshot.url,
                title: snapshot.title,
                timestamp: snapshot.timestamp,
                htmlLength: snapshot.html.length,
                cookiesCount: snapshot.cookiesArray.length
            }
        });
    } catch (error) {
        console.error('❌ Failed to create snapshot:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.post('/api/session/clone/:sessionId', async (req, res) => {
    try {
        const sessionId = req.params.sessionId;
        console.log(`🔄 Creating cloned session: ${sessionId}`);

        const cloneSession = await snapshotManager.createClonedSession(sessionId);

        res.json({
            success: true,
            sessionId: sessionId,
            createdAt: cloneSession.createdAt,
            message: 'Cloned session created successfully'
        });
    } catch (error) {
        console.error('❌ Failed to create cloned session:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.get('/api/session/clone/:sessionId/html', async (req, res) => {
    try {
        const sessionId = req.params.sessionId;
        const sessionData = await snapshotManager.getClonedSessionHTML(sessionId);

        // Inject base tag to fix relative URLs
        const baseUrl = new URL(sessionData.url).origin;
        const modifiedHtml = sessionData.html.replace(
            '<head>',
            `<head><base href="${baseUrl}/">`
        );

        res.setHeader('Content-Type', 'text/html');
        res.send(modifiedHtml);

    } catch (error) {
        console.error('❌ Failed to get cloned session HTML:', error.message);
        res.status(500).send(`
            <html>
                <body style="font-family: Arial; padding: 20px; text-align: center;">
                    <h1>❌ Session Error</h1>
                    <p>Failed to load cloned session: ${error.message}</p>
                    <p><a href="/kasm-clone">Create New Session</a></p>
                </body>
            </html>
        `);
    }
});

app.delete('/api/session/clone/:sessionId', async (req, res) => {
    try {
        const sessionId = req.params.sessionId;
        await snapshotManager.removeClonedSession(sessionId);

        res.json({
            success: true,
            message: `Session ${sessionId} removed`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

app.get('/api/sessions/status', async (req, res) => {
    try {
        res.json({
            activeClones: snapshotManager.getActiveSessionsCount(),
            snapshotExists: !!snapshotManager.snapshotData,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// KASM-style Clone Interface
app.get('/kasm-clone', (req, res) => {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - KASM-style Session Cloning</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8fafc; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .header h1 { font-size: 24px; margin-bottom: 10px; }
        .header p { opacity: 0.9; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { background: #4f46e5; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; margin: 5px; }
        .btn:hover { background: #4338ca; }
        .btn-success { background: #10b981; }
        .btn-success:hover { background: #059669; }
        .btn-danger { background: #ef4444; }
        .btn-danger:hover { background: #dc2626; }
        .status { padding: 10px; border-radius: 6px; margin: 10px 0; }
        .status.success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .status.error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .status.info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .session-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .session-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 15px; }
        .session-card h3 { color: #1e293b; margin-bottom: 10px; }
        .session-card p { color: #64748b; font-size: 14px; margin-bottom: 5px; }
        .loading { text-align: center; padding: 20px; color: #64748b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚛 DAT Load Board - KASM-style Session Cloning</h1>
            <p>Create isolated session snapshots like KASM workspaces</p>
        </div>

        <div class="card">
            <h2>📸 Session Snapshot Management</h2>
            <div id="status"></div>

            <div style="margin: 20px 0;">
                <button class="btn" onclick="initializeSnapshot()">🎯 Initialize Snapshot Manager</button>
                <button class="btn" onclick="createSnapshot()">📸 Create New Snapshot</button>
                <button class="btn" onclick="checkStatus()">📊 Check Status</button>
            </div>
        </div>

        <div class="card">
            <h2>🔄 Create Cloned Session</h2>
            <div style="margin: 20px 0;">
                <input type="text" id="sessionId" placeholder="Enter Session ID (e.g., user-123)" style="padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; width: 300px; margin-right: 10px;">
                <button class="btn btn-success" onclick="createClone()">🔄 Create Clone</button>
            </div>
            <div id="clone-status"></div>
        </div>

        <div class="card">
            <h2>👥 Active Cloned Sessions</h2>
            <div id="sessions-list" class="session-list">
                <div class="loading">Loading sessions...</div>
            </div>
            <button class="btn" onclick="refreshSessions()">🔄 Refresh</button>
        </div>
    </div>

    <script>
        let activeSessions = new Map();

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = \`<div class="status \${type}">\${message}</div>\`;
        }

        function showCloneStatus(message, type = 'info') {
            const statusDiv = document.getElementById('clone-status');
            statusDiv.innerHTML = \`<div class="status \${type}">\${message}</div>\`;
        }

        async function initializeSnapshot() {
            showStatus('🎯 Initializing snapshot manager...', 'info');

            try {
                const response = await fetch('/api/snapshot/initialize', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showStatus('✅ Snapshot manager initialized successfully!', 'success');
                } else {
                    showStatus(\`❌ Failed to initialize: \${data.error}\`, 'error');
                }
            } catch (error) {
                showStatus(\`❌ Error: \${error.message}\`, 'error');
            }
        }

        async function createSnapshot() {
            showStatus('📸 Creating session snapshot...', 'info');

            try {
                const response = await fetch('/api/snapshot/create', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showStatus(\`✅ Snapshot created! URL: \${data.snapshot.url}\`, 'success');
                } else {
                    showStatus(\`❌ Failed to create snapshot: \${data.error}\`, 'error');
                }
            } catch (error) {
                showStatus(\`❌ Error: \${error.message}\`, 'error');
            }
        }

        async function createClone() {
            const sessionId = document.getElementById('sessionId').value.trim();

            if (!sessionId) {
                showCloneStatus('❌ Please enter a session ID', 'error');
                return;
            }

            showCloneStatus(\`🔄 Creating cloned session: \${sessionId}...\`, 'info');

            try {
                const response = await fetch(\`/api/session/clone/\${sessionId}\`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showCloneStatus(\`✅ Cloned session created: \${sessionId}\`, 'success');

                    // Add to active sessions
                    activeSessions.set(sessionId, {
                        sessionId,
                        createdAt: data.createdAt,
                        url: \`/api/session/clone/\${sessionId}/html\`
                    });

                    // Clear input
                    document.getElementById('sessionId').value = '';

                    // Refresh sessions list
                    refreshSessions();

                } else {
                    showCloneStatus(\`❌ Failed to create clone: \${data.error}\`, 'error');
                }
            } catch (error) {
                showCloneStatus(\`❌ Error: \${error.message}\`, 'error');
            }
        }

        async function checkStatus() {
            try {
                const response = await fetch('/api/sessions/status');
                const data = await response.json();

                showStatus(\`📊 Active clones: \${data.activeClones} | Snapshot exists: \${data.snapshotExists ? 'Yes' : 'No'}\`, 'info');
            } catch (error) {
                showStatus(\`❌ Error checking status: \${error.message}\`, 'error');
            }
        }

        async function removeSession(sessionId) {
            if (!confirm(\`Remove session \${sessionId}?\`)) return;

            try {
                const response = await fetch(\`/api/session/clone/\${sessionId}\`, { method: 'DELETE' });
                const data = await response.json();

                if (data.success) {
                    activeSessions.delete(sessionId);
                    refreshSessions();
                } else {
                    alert(\`Failed to remove session: \${data.error}\`);
                }
            } catch (error) {
                alert(\`Error: \${error.message}\`);
            }
        }

        function refreshSessions() {
            const sessionsList = document.getElementById('sessions-list');

            if (activeSessions.size === 0) {
                sessionsList.innerHTML = '<div class="loading">No active sessions</div>';
                return;
            }

            let html = '';
            for (const [sessionId, session] of activeSessions) {
                html += \`
                    <div class="session-card">
                        <h3>🔄 \${sessionId}</h3>
                        <p><strong>Created:</strong> \${new Date(session.createdAt).toLocaleString()}</p>
                        <p><strong>URL:</strong> <a href="\${session.url}" target="_blank">Open Session</a></p>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-success" onclick="window.open('\${session.url}', '_blank')">🚀 Open</button>
                            <button class="btn btn-danger" onclick="removeSession('\${sessionId}')">🗑️ Remove</button>
                        </div>
                    </div>
                \`;
            }

            sessionsList.innerHTML = html;
        }

        // Auto-refresh status every 30 seconds
        setInterval(checkStatus, 30000);

        // Initial status check
        checkStatus();
    </script>
</body>
</html>`;

    res.send(html);
});

// Remote Control Interface (REAL browser control)
app.get('/remote-control', (req, res) => {
    const fs = require('fs');
    const html = fs.readFileSync(path.join(__dirname, 'remote-control.html'), 'utf8');
    res.send(html);
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// URL rewriting map for DAT subdomains
const urlRewriteMap = {
    'https://shared-content-cargo.dat.com': '/shared-content-cargo',
    'https://shared-content.prod.dat.com': '/shared-content',
    'https://one.prod-my-shipments.prod.dat.com': '/my-shipments',
    'https://one.prod-private-network.prod.dat.com': '/private-network',
    'https://one.freight-search-prod.prod.dat.com': '/freight-search',
    'https://one.company-search-prod.prod.dat.com': '/company-search',
    'https://one.dashboard.dat.com': '/dashboard-assets',
    'https://freight.api.dat.com': '/freight-api',
    'https://identity.api.dat.com': '/identity-api',
    'https://js.api.here.com': '/here-maps',
    'https://maps.googleapis.com': '/google-maps',
    'https://one.dat.com': '/dat-one',
    'https://login.dat.com': '/dat-login',
    'https://www.dat.com': '/dat-main'
};

// Function to rewrite URLs in HTML content
function rewriteURLsInContent(content, contentType) {
    if (!contentType || !contentType.includes('text/html')) {
        return content;
    }

    let rewrittenContent = content;

    // Rewrite absolute URLs to use our proxy
    for (const [originalUrl, proxyPath] of Object.entries(urlRewriteMap)) {
        const regex = new RegExp(originalUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        rewrittenContent = rewrittenContent.replace(regex, proxyPath);
    }

    return rewrittenContent;
}

// Custom middleware to remove X-Frame-Options and CSP headers
const removeFrameBlocking = (proxyRes, req, res) => {
    // Remove headers that block iframe embedding
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    delete proxyRes.headers['content-security-policy-report-only'];

    // Add headers to allow iframe embedding and fix CORS
    proxyRes.headers['x-frame-options'] = 'ALLOWALL';
    proxyRes.headers['access-control-allow-origin'] = '*';
    proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['access-control-allow-headers'] = '*';

    // Fix caching issues that might prevent JS/CSS loading
    if (req.originalUrl.includes('.js') || req.originalUrl.includes('.css')) {
        proxyRes.headers['cache-control'] = 'no-cache, no-store, must-revalidate';
        proxyRes.headers['pragma'] = 'no-cache';
        proxyRes.headers['expires'] = '0';
    }

    console.log(`📡 Proxied: ${req.method} ${req.originalUrl} -> Status: ${proxyRes.statusCode} (${proxyRes.headers['content-type'] || 'unknown'})`);
};

// Proxy configuration for DAT One
const datOneProxy = createProxyMiddleware({
    target: 'https://one.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        '^/dat-one': '', // Remove /dat-one prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error('❌ DAT One Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Login
const datLoginProxy = createProxyMiddleware({
    target: 'https://login.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        '^/dat-login': '', // Remove /dat-login prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error('❌ DAT Login Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Main Site
const datMainProxy = createProxyMiddleware({
    target: 'https://www.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    pathRewrite: {
        '^/dat-main': '', // Remove /dat-main prefix
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error('❌ DAT Main Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Comprehensive proxy for all DAT subdomains
const createDATProxy = (target, pathPrefix) => createProxyMiddleware({
    target: target,
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        [`^${pathPrefix}`]: '', // Remove prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error(`❌ Proxy Error for ${pathPrefix}:`, err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Middleware to handle authentication bypass and missing files
app.use((req, res, next) => {
    // Handle callback URL mismatch by redirecting to a working page
    if (req.path === '/callback') {
        console.log(`🔄 Intercepting callback redirect, sending to DAT main page`);
        return res.redirect('/dat-main/');
    }

    // Check if this is a JS/CSS file request that should be under /dat-one/
    if ((req.path.endsWith('.js') || req.path.endsWith('.css')) && !req.path.startsWith('/dat-')) {
        console.log(`🔄 Redirecting ${req.path} to /dat-one${req.path}`);
        return res.redirect(`/dat-one${req.path}`);
    }

    next();
});

// Apply comprehensive proxy middleware
app.use('/dat-one', datOneProxy);
app.use('/dat-login', datLoginProxy);
app.use('/dat-main', datMainProxy);

// Additional DAT subdomains
app.use('/shared-content-cargo', createDATProxy('https://shared-content-cargo.dat.com', '/shared-content-cargo'));
app.use('/shared-content', createDATProxy('https://shared-content.prod.dat.com', '/shared-content'));
app.use('/my-shipments', createDATProxy('https://one.prod-my-shipments.prod.dat.com', '/my-shipments'));
app.use('/private-network', createDATProxy('https://one.prod-private-network.prod.dat.com', '/private-network'));
app.use('/freight-search', createDATProxy('https://one.freight-search-prod.prod.dat.com', '/freight-search'));
app.use('/company-search', createDATProxy('https://one.company-search-prod.prod.dat.com', '/company-search'));
app.use('/dashboard-assets', createDATProxy('https://one.dashboard.dat.com', '/dashboard-assets'));
app.use('/freight-api', createDATProxy('https://freight.api.dat.com', '/freight-api'));
app.use('/identity-api', createDATProxy('https://identity.api.dat.com', '/identity-api'));

// External services (optional - may have CORS issues)
app.use('/here-maps', createDATProxy('https://js.api.here.com', '/here-maps'));
app.use('/google-maps', createDATProxy('https://maps.googleapis.com', '/google-maps'));

// Special route for demo/test access without authentication
app.get('/dat-demo', (req, res) => {
    res.send(`
        <html>
        <head>
            <title>DAT Demo Access</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
                .btn { background: #0066cc; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 10px; cursor: pointer; }
                iframe { width: 100%; height: 600px; border: 1px solid #ddd; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 DAT Demo Access</h1>
                <p>This page provides demo access to DAT services without authentication requirements.</p>

                <button class="btn" onclick="loadDemo('main')">Load DAT Main Site</button>
                <button class="btn" onclick="loadDemo('public')">Load DAT Public Pages</button>
                <button class="btn" onclick="loadDemo('info')">Load DAT Info</button>

                <iframe id="demoFrame" src="/dat-main/"></iframe>

                <script>
                    function loadDemo(type) {
                        const frame = document.getElementById('demoFrame');
                        switch(type) {
                            case 'main':
                                frame.src = '/dat-main/';
                                break;
                            case 'public':
                                frame.src = '/dat-main/solutions';
                                break;
                            case 'info':
                                frame.src = '/dat-main/about';
                                break;
                        }
                    }
                </script>
            </div>
        </body>
        </html>
    `);
});

// Load the master session data (updated every 5 minutes by master session manager)
function loadMasterSession() {
    try {
        const fs = require('fs');

        // Try to load from master session first
        let sessionData;
        try {
            sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
            console.log('🔐 Loading MASTER session data (live 24/7)...');
        } catch (e) {
            // Fallback to manual session if master not available
            sessionData = JSON.parse(fs.readFileSync('dat-session.json', 'utf8'));
            console.log('🔐 Loading fallback manual session data...');
        }

        console.log(`📍 Session URL: ${sessionData.currentUrl}`);
        console.log(`🍪 Cookies: ${sessionData.cookies ? sessionData.cookies.length : sessionData.cookieCount || 'N/A'}`);
        console.log(`📊 LocalStorage: ${sessionData.localStorage.length} chars`);
        console.log(`⏰ Last Updated: ${sessionData.timestamp}`);

        // Create a special session ID for the master session
        const masterSessionId = 'master-session';
        authenticatedSessions.set(masterSessionId, sessionData);

        console.log(`✅ Master session loaded with ID: ${masterSessionId}`);
        return masterSessionId;

    } catch (error) {
        console.error('❌ Failed to load master session:', error.message);
        return null;
    }
}

// Auto-reload master session data every 30 seconds
setInterval(() => {
    try {
        const fs = require('fs');
        const sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
        authenticatedSessions.set('master-session', sessionData);
        console.log('🔄 Master session data auto-reloaded');
    } catch (error) {
        console.log('⚠️ Failed to auto-reload master session data');
    }
}, 30000);

// API endpoint to use master authenticated session
app.get('/api/use-master-session', (req, res) => {
    const masterSessionId = loadMasterSession();

    if (masterSessionId) {
        const sessionData = authenticatedSessions.get(masterSessionId);
        const proxyUrl = `http://${req.get('host')}/authenticated/${masterSessionId}`;

        res.json({
            success: true,
            sessionId: masterSessionId,
            proxyUrl: proxyUrl,
            lastUpdated: sessionData.timestamp,
            message: 'Master session loaded successfully (live 24/7)'
        });
    } else {
        res.status(500).json({
            success: false,
            error: 'Failed to load master session'
        });
    }
});

// API endpoint to create authenticated session
app.post('/api/create-authenticated-session', express.json(), (req, res) => {
    try {
        const { cookies, localStorage, sessionStorage, authHeader, currentUrl } = req.body;

        if (!cookies && !authHeader) {
            return res.json({ success: false, error: 'No authentication data provided' });
        }

        // Generate session ID
        const sessionId = require('crypto').randomUUID();

        // Store session data
        authenticatedSessions.set(sessionId, {
            cookies: cookies,
            localStorage: localStorage,
            sessionStorage: sessionStorage,
            authHeader: authHeader,
            currentUrl: currentUrl,
            created: new Date().toISOString()
        });

        console.log(`🔐 Created authenticated session: ${sessionId}`);

        res.json({
            success: true,
            sessionId: sessionId,
            proxyUrl: `http://*************:3004/authenticated/${sessionId}`
        });

    } catch (error) {
        console.error('❌ Error creating authenticated session:', error);
        res.json({ success: false, error: error.message });
    }
});

// API endpoint to test session validity
app.post('/api/test-session', express.json(), (req, res) => {
    try {
        const { cookies, authHeader } = req.body;

        // Basic validation
        if (!cookies && !authHeader) {
            return res.json({ valid: false, reason: 'No authentication data provided' });
        }

        // Check for common DAT session indicators
        const hasValidCookies = cookies && (
            cookies.includes('auth0') ||
            cookies.includes('session') ||
            cookies.includes('token') ||
            cookies.includes('dat')
        );

        const hasValidAuth = authHeader && (
            authHeader.startsWith('Bearer ') ||
            authHeader.includes('token')
        );

        if (hasValidCookies || hasValidAuth) {
            res.json({ valid: true, reason: 'Session data looks valid' });
        } else {
            res.json({ valid: false, reason: 'No recognizable session tokens found' });
        }

    } catch (error) {
        console.error('❌ Error testing session:', error);
        res.json({ valid: false, reason: error.message });
    }
});

// Create DAT session and get Bearer token
async function createDATSession(sessionData) {
    try {
        const response = await fetch('https://identity.api.dat.com/usurp/v1/session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cookie': sessionData.cookies,
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            body: JSON.stringify({
                userId: 'auth0|64355ce3d8f24d23556f11f9',
                appId: 'DAT One Web',
                deviceType: 'desktop',
                agentDeviceId: '15746b34-803e-1bb3-5be6-960a073fc692'
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log(`🔐 Created DAT session: ${data.sessionId}`);
            return data.sessionId;
        } else {
            console.error('❌ Failed to create DAT session:', response.status);
            return null;
        }
    } catch (error) {
        console.error('❌ Error creating DAT session:', error);
        return null;
    }
}

// Create authenticated proxy middleware
const createAuthenticatedProxy = (sessionData, target) => createProxyMiddleware({
    target: target,
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    onProxyReq: async (proxyReq, req, res) => {
        // Add the user's cookies to the request
        if (sessionData.cookies) {
            proxyReq.setHeader('Cookie', sessionData.cookies);
        }

        // For API requests, add Authorization header with session token
        if (req.originalUrl.includes('api.dat.com')) {
            if (!sessionData.bearerToken) {
                sessionData.bearerToken = await createDATSession(sessionData);
            }

            if (sessionData.bearerToken) {
                proxyReq.setHeader('Authorization', `Bearer ${sessionData.bearerToken}`);
                console.log(`🔐 Added Bearer token to API request: ${req.originalUrl}`);
            }
        }

        // Add proper headers
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');

        console.log(`🔐 Authenticated request: ${req.method} ${req.originalUrl}`);
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error(`❌ Authenticated Proxy Error:`, err.message);
        res.status(500).send('Authenticated Proxy Error: ' + err.message);
    }
});

// Authenticated proxy routes
app.use('/authenticated/:sessionId/dat-one', (req, res, next) => {
    const sessionId = req.params.sessionId;
    const sessionData = authenticatedSessions.get(sessionId);

    if (!sessionData) {
        return res.status(404).send('Session not found or expired');
    }

    // Create authenticated proxy for this session
    const authProxy = createAuthenticatedProxy(sessionData, 'https://one.dat.com');

    // Remove the session ID from the path
    req.url = req.url.replace(`/authenticated/${sessionId}/dat-one`, '');
    if (!req.url.startsWith('/')) req.url = '/' + req.url;

    authProxy(req, res, next);
});

app.use('/authenticated/:sessionId/dat-login', (req, res, next) => {
    const sessionId = req.params.sessionId;
    const sessionData = authenticatedSessions.get(sessionId);

    if (!sessionData) {
        return res.status(404).send('Session not found or expired');
    }

    const authProxy = createAuthenticatedProxy(sessionData, 'https://login.dat.com');
    req.url = req.url.replace(`/authenticated/${sessionId}/dat-login`, '');
    if (!req.url.startsWith('/')) req.url = '/' + req.url;

    authProxy(req, res, next);
});

// Main authenticated proxy route (catch-all)
app.get('/authenticated/:sessionId', (req, res) => {
    const sessionId = req.params.sessionId;

    const sessionData = authenticatedSessions.get(sessionId);
    if (!sessionData) {
        return res.status(404).send(`
            <html>
            <head><title>Session Not Found</title></head>
            <body>
                <h1>❌ Session Not Found</h1>
                <p>Session ID: <code>${sessionId}</code></p>
                <p>The session may have expired or the server was restarted.</p>
                <p><a href="/session-capture.html">Create New Session</a></p>
            </body>
            </html>
        `);
    }

    console.log(`🔐 Using authenticated session ${sessionId} for main page`);

    // Show authenticated dashboard
    res.send(`
        <html>
        <head>
            <title>🔐 Authenticated DAT Access</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .header { background: #2d2d2d; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .btn { background: #0066cc; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
                iframe { width: 100%; height: 800px; border: 2px solid #ddd; border-radius: 4px; margin: 10px 0; }
                .info { background: #d4edda; padding: 15px; border-radius: 4px; margin: 15px 0; color: #155724; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Authenticated DAT Access</h1>
                    <p>Session ID: <code>${sessionId}</code></p>
                    <p>User: <strong>Hakob Ter-Sahakyan (Reize LLC)</strong></p>
                </div>

                <div class="info">
                    <h4>✅ Authentication Status: ACTIVE</h4>
                    <p>Your DAT session has been successfully captured and is being used for authenticated access.</p>
                </div>

                <div>
                    <a href="/authenticated/${sessionId}/dat-one/dashboard" class="btn">📊 Load DAT Dashboard</a>
                    <a href="/authenticated/${sessionId}/dat-one/search-loads-ow" class="btn">🚛 Search Loads</a>
                    <a href="/authenticated/${sessionId}/dat-one/" class="btn">🏠 DAT Home</a>
                </div>

                <iframe src="/authenticated/${sessionId}/dat-one/dashboard" onload="console.log('Authenticated DAT loaded!')"></iframe>
            </div>
        </body>
        </html>
    `);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        proxies: {
            'dat-one': 'https://one.dat.com',
            'dat-login': 'https://login.dat.com',
            'dat-main': 'https://www.dat.com'
        },
        authBypass: 'Available at /dat-demo',
        sessionCapture: 'Available at /session-capture.html',
        authenticatedSessions: authenticatedSessions.size
    });
});

// API endpoint to test proxy connectivity
app.get('/api/test-proxy/:target', async (req, res) => {
    const { target } = req.params;
    const targetUrls = {
        'dat-one': 'https://one.dat.com',
        'dat-login': 'https://login.dat.com',
        'dat-main': 'https://www.dat.com'
    };
    
    const targetUrl = targetUrls[target];
    if (!targetUrl) {
        return res.status(400).json({ error: 'Invalid target' });
    }
    
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(targetUrl, {
            method: 'HEAD',
            timeout: 10000
        });
        
        res.json({
            target: target,
            url: targetUrl,
            status: response.status,
            accessible: response.ok,
            headers: Object.fromEntries(response.headers.entries())
        });
    } catch (error) {
        res.json({
            target: target,
            url: targetUrl,
            accessible: false,
            error: error.message
        });
    }
});

// Default route
app.get('/', (req, res) => {
    res.send(`
        <h1>🚀 DAT Comprehensive iframe Proxy Server</h1>
        <p>Reverse proxy to bypass X-Frame-Options for ALL DAT websites and subdomains</p>
        <h2>Main DAT Proxies:</h2>
        <ul>
            <li><a href="/dat-one/dashboard" target="_blank">DAT One Dashboard</a> → <code>/dat-one/*</code></li>
            <li><a href="/dat-login" target="_blank">DAT Login</a> → <code>/dat-login/*</code></li>
            <li><a href="/dat-main" target="_blank">DAT Main Site</a> → <code>/dat-main/*</code></li>
        </ul>
        <h2>DAT Subdomain Proxies:</h2>
        <ul>
            <li><strong>Assets:</strong> <code>/shared-content-cargo/*</code> → shared-content-cargo.dat.com</li>
            <li><strong>Shared Content:</strong> <code>/shared-content/*</code> → shared-content.prod.dat.com</li>
            <li><strong>My Shipments:</strong> <code>/my-shipments/*</code> → one.prod-my-shipments.prod.dat.com</li>
            <li><strong>Private Network:</strong> <code>/private-network/*</code> → one.prod-private-network.prod.dat.com</li>
            <li><strong>Freight Search:</strong> <code>/freight-search/*</code> → one.freight-search-prod.prod.dat.com</li>
            <li><strong>Company Search:</strong> <code>/company-search/*</code> → one.company-search-prod.prod.dat.com</li>
            <li><strong>Dashboard Assets:</strong> <code>/dashboard-assets/*</code> → one.dashboard.dat.com</li>
        </ul>
        <h2>DAT API Proxies:</h2>
        <ul>
            <li><strong>Freight API:</strong> <code>/freight-api/*</code> → freight.api.dat.com</li>
            <li><strong>Identity API:</strong> <code>/identity-api/*</code> → identity.api.dat.com</li>
        </ul>
        <h2>External Service Proxies:</h2>
        <ul>
            <li><strong>HERE Maps:</strong> <code>/here-maps/*</code> → js.api.here.com</li>
            <li><strong>Google Maps:</strong> <code>/google-maps/*</code> → maps.googleapis.com</li>
        </ul>
        <h2>Test Pages:</h2>
        <ul>
            <li><a href="/iframe-test.html">iframe Embedding Test</a></li>
            <li><a href="/loading-fix.html">Loading Screen Fix Test</a></li>
            <li><a href="/session-capture.html">🔐 Session Capture Tool</a></li>
            <li><a href="/dat-demo">Demo Access (No Auth)</a></li>
            <li><a href="/health">Health Check</a></li>
        </ul>
        <p><strong>Usage:</strong> Use these proxy URLs in your iframes to bypass X-Frame-Options restrictions.</p>
        <p><strong>Note:</strong> This comprehensive proxy handles ALL DAT subdomains and micro-frontends!</p>
    `);
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 DAT Comprehensive iframe Proxy Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log(`📡 Proxying ALL DAT subdomains:`);
    console.log(`   /dat-one/* → https://one.dat.com/*`);
    console.log(`   /dat-login/* → https://login.dat.com/*`);
    console.log(`   /dat-main/* → https://www.dat.com/*`);
    console.log(`   /shared-content-cargo/* → https://shared-content-cargo.dat.com/*`);
    console.log(`   /shared-content/* → https://shared-content.prod.dat.com/*`);
    console.log(`   /my-shipments/* → https://one.prod-my-shipments.prod.dat.com/*`);
    console.log(`   /private-network/* → https://one.prod-private-network.prod.dat.com/*`);
    console.log(`   /freight-search/* → https://one.freight-search-prod.prod.dat.com/*`);
    console.log(`   /company-search/* → https://one.company-search-prod.prod.dat.com/*`);
    console.log(`   /dashboard-assets/* → https://one.dashboard.dat.com/*`);
    console.log(`   /freight-api/* → https://freight.api.dat.com/*`);
    console.log(`   /identity-api/* → https://identity.api.dat.com/*`);
    console.log(`   /here-maps/* → https://js.api.here.com/*`);
    console.log(`   /google-maps/* → https://maps.googleapis.com/*`);
    console.log(`🎯 Ready to handle ALL DAT micro-frontends and fix loading screen!`);
});
