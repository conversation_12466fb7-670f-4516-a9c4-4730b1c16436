const puppeteer = require('puppeteer');
const fs = require('fs');

class ExistingSessionToMaster {
    constructor() {
        this.masterBrowser = null;
        this.masterPage = null;
        this.isAuthenticated = false;
        this.sessionData = null;
        this.keepAliveInterval = null;
    }

    async convertExistingSession() {
        console.log('🔄 Converting existing authenticated session to master session...');
        
        try {
            // Load the existing session data
            this.sessionData = JSON.parse(fs.readFileSync('dat-session.json', 'utf8'));
            console.log('✅ Loaded existing session data');
            console.log(`📍 Session URL: ${this.sessionData.currentUrl}`);
            console.log(`🍪 Cookies: ${this.sessionData.cookieCount}`);
            
            // Initialize browser with existing session
            await this.initializeBrowserWithSession();
            
            // Verify it's still authenticated
            await this.verifyAndMaintainSession();
            
            if (this.isAuthenticated) {
                console.log('🎉 Successfully converted to master session!');
                await this.startKeepAlive();
                return true;
            } else {
                console.log('❌ Session conversion failed - session may have expired');
                return false;
            }
            
        } catch (error) {
            console.error('❌ Error converting session:', error.message);
            return false;
        }
    }

    async initializeBrowserWithSession() {
        console.log('🚀 Initializing browser with existing session...');
        
        this.masterBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-gpu',
                '--disable-extensions',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1280, height: 720 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Set cookies from existing session
        const cookieStrings = this.sessionData.cookies.split('; ');
        const cookies = [];
        
        for (const cookieString of cookieStrings) {
            const [name, value] = cookieString.split('=');
            if (name && value) {
                cookies.push({
                    name: name.trim(),
                    value: value.trim(),
                    domain: '.dat.com',
                    path: '/'
                });
            }
        }

        await this.masterPage.setCookie(...cookies);
        console.log(`🍪 Set ${cookies.length} cookies from existing session`);

        // Set localStorage and sessionStorage
        await this.masterPage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, this.sessionData.localStorage, this.sessionData.sessionStorage);

        console.log('📊 Set localStorage and sessionStorage from existing session');
    }

    async verifyAndMaintainSession() {
        console.log('🔍 Verifying existing session is still active...');
        
        // Navigate to dashboard
        await this.masterPage.goto('https://one.dat.com/dashboard', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Take screenshot to see current state
        await this.masterPage.screenshot({ path: 'master-conversion-check.png', fullPage: true });
        console.log('📸 Session verification screenshot saved');

        // Handle any popups that might appear
        await this.handleAllPopups();

        // Check final state
        const currentUrl = this.masterPage.url();
        const pageTitle = await this.masterPage.title();
        
        console.log(`📍 Current URL: ${currentUrl}`);
        console.log(`📄 Page Title: ${pageTitle}`);

        // Verify authentication
        const isOnDashboard = currentUrl.includes('one.dat.com') && !currentUrl.includes('login');
        const hasValidTitle = pageTitle.includes('DAT') || pageTitle.includes('One');
        
        this.isAuthenticated = isOnDashboard && hasValidTitle;
        
        if (this.isAuthenticated) {
            console.log('✅ Session is still authenticated!');
            await this.captureUpdatedSessionData();
            await this.masterPage.screenshot({ path: 'master-session-verified.png', fullPage: true });
            console.log('📸 Verified master session screenshot saved');
        } else {
            console.log('❌ Session appears to have expired');
            await this.masterPage.screenshot({ path: 'master-session-expired.png', fullPage: true });
            console.log('📸 Expired session screenshot saved');
        }
    }

    async handleAllPopups() {
        console.log('🔍 Handling any popups that might appear...');
        
        // Handle multiple rounds of popups
        for (let round = 0; round < 3; round++) {
            console.log(`🔄 Popup handling round ${round + 1}...`);
            
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Close any blocking popups
            await this.closeAnyPopups();
            
            // Handle "Login anyway" if present
            await this.handleLoginAnyway();
            
            // Handle "Click here to post trucks" popup
            await this.handlePostTrucksPopup();
            
            // Take screenshot after each round
            await this.masterPage.screenshot({ path: `master-popup-round-${round + 1}.png`, fullPage: true });
            console.log(`📸 Popup round ${round + 1} screenshot saved`);
        }
    }

    async closeAnyPopups() {
        console.log('🚫 Closing any visible popups...');
        
        const closeSelectors = [
            'button[aria-label="Close"]',
            'button[title="Close"]',
            '.close-button',
            '.modal-close',
            '.popup-close',
            '[data-testid*="close"]',
            'button:contains("×")',
            'button:contains("✕")',
            'button:contains("Close")',
            'button:contains("Dismiss")',
            'button:contains("No thanks")',
            'button:contains("Skip")',
            '.fa-times',
            '.fa-close'
        ];
        
        for (const selector of closeSelectors) {
            try {
                const closeButtons = await this.masterPage.$$(selector);
                
                for (const closeButton of closeButtons) {
                    const isVisible = await this.masterPage.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);
                        return rect.width > 0 && rect.height > 0 && 
                               style.visibility !== 'hidden' && 
                               style.display !== 'none';
                    }, closeButton);
                    
                    if (isVisible) {
                        try {
                            await closeButton.click();
                            console.log(`✅ Closed popup with selector: ${selector}`);
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        } catch (e) {
                            // Try alternative click
                            try {
                                await this.masterPage.evaluate(el => el.click(), closeButton);
                                console.log(`✅ Closed popup with selector: ${selector} (alternative)`);
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            } catch (e2) {
                                // Continue
                            }
                        }
                    }
                }
            } catch (e) {
                // Continue to next selector
            }
        }
    }

    async handleLoginAnyway() {
        console.log('🔍 Checking for "Login anyway" button...');
        
        const buttons = await this.masterPage.$$('button, a, div[role="button"]');
        
        for (const button of buttons) {
            const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, button);
            if (text && text.toLowerCase().includes('login anyway')) {
                const isVisible = await this.masterPage.evaluate(el => {
                    const rect = el.getBoundingClientRect();
                    const style = window.getComputedStyle(el);
                    return rect.width > 0 && rect.height > 0 && 
                           style.visibility !== 'hidden' && 
                           style.display !== 'none';
                }, button);
                
                if (isVisible) {
                    try {
                        await button.click();
                        console.log('✅ Clicked "Login anyway" button');
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        return;
                    } catch (e) {
                        try {
                            await this.masterPage.evaluate(el => el.click(), button);
                            console.log('✅ Clicked "Login anyway" button (alternative)');
                            await new Promise(resolve => setTimeout(resolve, 3000));
                            return;
                        } catch (e2) {
                            // Continue
                        }
                    }
                }
            }
        }
    }

    async handlePostTrucksPopup() {
        console.log('🔍 Checking for "post trucks" popup...');
        
        const allElements = await this.masterPage.$$('button, a, div, span');
        
        for (const element of allElements) {
            const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, element);
            if (text && (
                text.toLowerCase().includes('click here to post') ||
                text.toLowerCase().includes('post your trucks') ||
                text.toLowerCase().includes('post trucks')
            )) {
                // Look for close button near this element
                const parent = await this.masterPage.evaluateHandle(el => el.closest('.modal, .popup, .dialog, [role="dialog"]'), element);
                
                if (parent) {
                    const closeButton = await parent.$('button[aria-label="Close"], .close-button, button:contains("×"), button:contains("Close")');
                    
                    if (closeButton) {
                        try {
                            await closeButton.click();
                            console.log('✅ Closed "post trucks" popup');
                            await new Promise(resolve => setTimeout(resolve, 2000));
                            return;
                        } catch (e) {
                            try {
                                await this.masterPage.evaluate(el => el.click(), closeButton);
                                console.log('✅ Closed "post trucks" popup (alternative)');
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                return;
                            } catch (e2) {
                                // Continue
                            }
                        }
                    }
                }
            }
        }
    }

    async captureUpdatedSessionData() {
        console.log('📊 Capturing updated session data...');
        
        const cookies = await this.masterPage.cookies();
        const storageData = await this.masterPage.evaluate(() => {
            return {
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                currentUrl: window.location.href
            };
        });
        
        this.sessionData = {
            cookies: cookies.map(c => `${c.name}=${c.value}`).join('; '),
            localStorage: storageData.localStorage,
            sessionStorage: storageData.sessionStorage,
            currentUrl: storageData.currentUrl,
            timestamp: new Date().toISOString(),
            cookieCount: cookies.length
        };
        
        // Save as master session data
        fs.writeFileSync('master-session-data.json', JSON.stringify(this.sessionData, null, 2));
        console.log('✅ Master session data captured and saved');
    }

    async startKeepAlive() {
        console.log('🔄 Starting keep-alive for converted master session...');
        
        this.keepAliveInterval = setInterval(async () => {
            try {
                console.log('💓 Master session keep-alive ping...');
                
                // Navigate to dashboard
                await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'domcontentloaded', timeout: 10000 });
                
                // Wait and handle popups
                await new Promise(resolve => setTimeout(resolve, 3000));
                await this.handleAllPopups();
                
                // Check if still authenticated
                const currentUrl = this.masterPage.url();
                if (currentUrl.includes('login')) {
                    console.log('❌ Master session expired! Need re-authentication.');
                    this.isAuthenticated = false;
                    clearInterval(this.keepAliveInterval);
                } else {
                    console.log('✅ Master session still active');
                    await this.captureUpdatedSessionData();
                }
                
            } catch (error) {
                console.error('⚠️ Keep-alive error:', error.message);
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    async cleanup() {
        console.log('🧹 Cleaning up master session...');
        
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
        }
        
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        
        console.log('✅ Cleanup complete');
    }
}

// Export for use
module.exports = ExistingSessionToMaster;

// Run if called directly
if (require.main === module) {
    const converter = new ExistingSessionToMaster();
    
    converter.convertExistingSession()
        .then(success => {
            if (success) {
                console.log('\n🎉 Master session is now running 24/7!');
                console.log('💓 Keep-alive active - session will stay authenticated');
                console.log('👥 Ready to serve multiple users');
                
                // Keep process alive
                console.log('\n🔄 Master session running in background...');
                console.log('Press Ctrl+C to stop');
                
                // Handle graceful shutdown
                process.on('SIGINT', async () => {
                    console.log('\n🛑 Shutting down master session...');
                    await converter.cleanup();
                    process.exit(0);
                });
                
                // Heartbeat
                setInterval(() => {
                    console.log(`💓 Master session heartbeat - ${new Date().toISOString()}`);
                }, 10 * 60 * 1000);
                
            } else {
                console.log('\n❌ Failed to convert session to master');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n❌ Conversion failed:', error.message);
            process.exit(1);
        });
}
