{"name": "dat-iframe-proxy", "version": "1.0.0", "description": "Reverse proxy for DAT websites to bypass X-Frame-Options", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "node-fetch": "^2.7.0", "puppeteer": "^24.11.1", "ws": "^8.18.3"}, "keywords": ["proxy", "iframe", "dat", "reverse-proxy"], "author": "DAT iframe Proxy", "license": "MIT"}