const puppeteer = require('puppeteer');
const fs = require('fs');

async function checkDATSession() {
    console.log('🔍 Checking DAT session status...');
    
    const browser = await puppeteer.launch({
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-gpu',
            '--disable-extensions',
            '--no-first-run',
            '--no-zygote',
            '--single-process'
        ]
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');

    try {
        // Load the saved session data
        let sessionData;
        try {
            sessionData = JSON.parse(fs.readFileSync('dat-session.json', 'utf8'));
            console.log('✅ Session data loaded from file');
        } catch (error) {
            console.error('❌ Failed to load session data:', error.message);
            return;
        }

        // Parse and set cookies
        const cookieStrings = sessionData.cookies.split('; ');
        const cookies = [];
        
        for (const cookieString of cookieStrings) {
            const [name, value] = cookieString.split('=');
            if (name && value) {
                cookies.push({
                    name: name.trim(),
                    value: value.trim(),
                    domain: '.dat.com',
                    path: '/'
                });
            }
        }

        // Set cookies before navigating
        await page.setCookie(...cookies);
        console.log(`🍪 Set ${cookies.length} cookies`);

        // Set localStorage and sessionStorage
        await page.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            // Set localStorage
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            // Set sessionStorage
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, sessionData.localStorage, sessionData.sessionStorage);

        console.log('📊 Set localStorage and sessionStorage');

        // Navigate to DAT dashboard
        console.log('📍 Navigating to DAT dashboard...');
        await page.goto('https://one.dat.com/dashboard', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 8000));

        const currentUrl = page.url();
        console.log(`📍 Current URL: ${currentUrl}`);

        // Take screenshot
        await page.screenshot({ 
            path: 'session-check-screenshot.png', 
            fullPage: true 
        });
        console.log('📸 Screenshot saved: session-check-screenshot.png');

        // Check if we're logged in by looking for specific elements
        const pageTitle = await page.title();
        console.log(`📄 Page Title: ${pageTitle}`);

        // Check for login indicators
        const isOnLoginPage = currentUrl.includes('login') || currentUrl.includes('auth');
        const hasLoginForm = await page.$('input[type="email"], input[name="username"]') !== null;
        const hasDashboardContent = await page.$('[data-testid*="dashboard"], .dashboard, .main-content') !== null;

        console.log('\n🔍 Session Status Analysis:');
        console.log(`📍 URL contains login: ${isOnLoginPage}`);
        console.log(`🔐 Has login form: ${hasLoginForm}`);
        console.log(`📊 Has dashboard content: ${hasDashboardContent}`);

        if (isOnLoginPage || hasLoginForm) {
            console.log('❌ SESSION EXPIRED - User is on login page');
            console.log('🔄 Need to re-authenticate');
        } else if (hasDashboardContent || pageTitle.includes('DAT')) {
            console.log('✅ SESSION ACTIVE - User is logged in');
            console.log('🎉 Dashboard is accessible');
        } else {
            console.log('⚠️ SESSION STATUS UNCLEAR - Check screenshot');
        }

        // Get page content snippet for analysis
        const bodyText = await page.evaluate(() => {
            return document.body.textContent.substring(0, 500);
        });
        
        console.log('\n📝 Page Content Preview:');
        console.log(bodyText.substring(0, 200) + '...');

        return {
            url: currentUrl,
            title: pageTitle,
            isLoggedIn: !isOnLoginPage && !hasLoginForm,
            hasLoginForm: hasLoginForm,
            hasDashboardContent: hasDashboardContent
        };

    } catch (error) {
        console.error('❌ Error checking session:', error.message);
        
        // Take error screenshot
        await page.screenshot({ path: 'session-check-error.png', fullPage: true });
        console.log('📸 Error screenshot saved: session-check-error.png');
        
        throw error;
    } finally {
        await browser.close();
        console.log('✅ Browser closed');
    }
}

// Run the session check
if (require.main === module) {
    checkDATSession()
        .then(result => {
            if (result) {
                console.log('\n🎯 Session Check Complete!');
                console.log('📸 Check the screenshot to verify the current state.');
            }
        })
        .catch(error => {
            console.error('\n❌ Session check failed:', error.message);
            process.exit(1);
        });
}

module.exports = { checkDATSession };
