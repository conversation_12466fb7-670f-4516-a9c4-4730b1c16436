const puppeteer = require('puppeteer');
const fs = require('fs');

async function handleLoginPopup() {
    console.log('🔍 Handling "Login anyway" popup...');
    
    const browser = await puppeteer.launch({
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security'
        ]
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');

    try {
        // Load the saved session data
        let sessionData;
        try {
            sessionData = JSON.parse(fs.readFileSync('dat-session.json', 'utf8'));
            console.log('✅ Session data loaded from file');
        } catch (error) {
            console.error('❌ Failed to load session data:', error.message);
            return;
        }

        // Parse and set cookies
        const cookieStrings = sessionData.cookies.split('; ');
        const cookies = [];
        
        for (const cookieString of cookieStrings) {
            const [name, value] = cookieString.split('=');
            if (name && value) {
                cookies.push({
                    name: name.trim(),
                    value: value.trim(),
                    domain: '.dat.com',
                    path: '/'
                });
            }
        }

        await page.setCookie(...cookies);
        console.log(`🍪 Set ${cookies.length} cookies`);

        // Set localStorage and sessionStorage
        await page.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, sessionData.localStorage, sessionData.sessionStorage);

        // Navigate to DAT dashboard
        console.log('📍 Navigating to DAT dashboard...');
        await page.goto('https://one.dat.com/dashboard', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Take screenshot to see current state
        await page.screenshot({ path: 'popup-handler-current-state.png', fullPage: true });
        console.log('📸 Current state screenshot saved');

        // Look for "Login anyway" button
        console.log('🔍 Looking for "Login anyway" button...');
        
        const loginAnywaySelectors = [
            'button:contains("Login anyway")',
            'button:contains("Log in anyway")', 
            'button:contains("Continue anyway")',
            '[data-testid*="login-anyway"]',
            'button[value*="anyway"]'
        ];
        
        let loginAnywayButton = null;
        
        // Search by text content
        const buttons = await page.$$('button, a, div[role="button"]');
        
        for (const button of buttons) {
            const text = await page.evaluate(el => el.textContent || el.innerText, button);
            if (text && (
                text.toLowerCase().includes('login anyway') || 
                text.toLowerCase().includes('log in anyway') ||
                text.toLowerCase().includes('continue anyway')
            )) {
                loginAnywayButton = button;
                console.log(`✅ Found "Login anyway" button: "${text}"`);
                break;
            }
        }
        
        if (loginAnywayButton) {
            console.log('🔐 Clicking "Login anyway" button...');
            
            try {
                await loginAnywayButton.click();
                console.log('✅ "Login anyway" button clicked');
            } catch (e) {
                // Try alternative click method
                await page.evaluate(el => el.click(), loginAnywayButton);
                console.log('✅ "Login anyway" button clicked (alternative method)');
            }
            
            // Wait for navigation/popup to close
            await new Promise(resolve => setTimeout(resolve, 8000));
            
            // Take screenshot after clicking
            await page.screenshot({ path: 'popup-handler-after-click.png', fullPage: true });
            console.log('📸 After click screenshot saved');
            
            // Check final state
            const currentUrl = page.url();
            console.log(`📍 Final URL: ${currentUrl}`);
            
            if (currentUrl.includes('one.dat.com') && !currentUrl.includes('login')) {
                console.log('✅ Successfully handled popup - now on dashboard!');
                
                // Update session data
                const newCookies = await page.cookies();
                const newStorageData = await page.evaluate(() => {
                    return {
                        localStorage: JSON.stringify(localStorage),
                        sessionStorage: JSON.stringify(sessionStorage),
                        currentUrl: window.location.href
                    };
                });
                
                const updatedSessionData = {
                    cookies: newCookies.map(c => `${c.name}=${c.value}`).join('; '),
                    localStorage: newStorageData.localStorage,
                    sessionStorage: newStorageData.sessionStorage,
                    currentUrl: newStorageData.currentUrl,
                    timestamp: new Date().toISOString(),
                    cookieCount: newCookies.length
                };
                
                // Save updated session
                fs.writeFileSync('dat-session-updated.json', JSON.stringify(updatedSessionData, null, 2));
                console.log('✅ Updated session data saved');
                
                return true;
            } else {
                console.log('⚠️ Still not on dashboard after popup handling');
                return false;
            }
            
        } else {
            console.log('ℹ️ No "Login anyway" popup found');
            
            // Check if we're already on dashboard
            const currentUrl = page.url();
            if (currentUrl.includes('one.dat.com') && !currentUrl.includes('login')) {
                console.log('✅ Already on dashboard - no popup needed');
                return true;
            } else {
                console.log('⚠️ Not on dashboard and no popup found');
                return false;
            }
        }

    } catch (error) {
        console.error('❌ Error handling popup:', error.message);
        
        await page.screenshot({ path: 'popup-handler-error.png', fullPage: true });
        console.log('📸 Error screenshot saved');
        
        return false;
    } finally {
        await browser.close();
        console.log('✅ Browser closed');
    }
}

// Run if called directly
if (require.main === module) {
    handleLoginPopup()
        .then(success => {
            if (success) {
                console.log('\n🎉 Popup handled successfully!');
            } else {
                console.log('\n❌ Failed to handle popup');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n❌ Popup handler failed:', error.message);
            process.exit(1);
        });
}

module.exports = { handleLoginPopup };
