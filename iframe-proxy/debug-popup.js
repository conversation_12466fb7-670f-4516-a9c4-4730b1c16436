const puppeteer = require('puppeteer');
const fs = require('fs');

async function debugBlockingPopup() {
    console.log('🔍 Debugging the blocking popup...');
    
    const browser = await puppeteer.launch({
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security'
        ]
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');

    try {
        // Load the existing session data
        const sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
        console.log('✅ Loaded master session data');

        // Set cookies
        if (sessionData.cookies && Array.isArray(sessionData.cookies)) {
            await page.setCookie(...sessionData.cookies);
            console.log(`🍪 Set ${sessionData.cookies.length} cookies`);
        } else {
            // Handle string format cookies
            const cookieStrings = sessionData.cookies.split('; ');
            const cookies = [];
            
            for (const cookieString of cookieStrings) {
                const [name, value] = cookieString.split('=');
                if (name && value) {
                    cookies.push({
                        name: name.trim(),
                        value: value.trim(),
                        domain: '.dat.com',
                        path: '/'
                    });
                }
            }
            await page.setCookie(...cookies);
            console.log(`🍪 Set ${cookies.length} cookies`);
        }

        // Set storage
        await page.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, sessionData.localStorage, sessionData.sessionStorage);

        // Navigate to dashboard
        console.log('📍 Navigating to DAT dashboard...');
        await page.goto('https://one.dat.com/dashboard', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Take initial screenshot
        await page.screenshot({ path: 'debug-initial-state.png', fullPage: true });
        console.log('📸 Initial state screenshot saved');

        // Get detailed information about all elements on the page
        console.log('🔍 Analyzing all elements on the page...');
        
        const allElements = await page.evaluate(() => {
            const elements = [];
            const allNodes = document.querySelectorAll('*');
            
            for (let i = 0; i < allNodes.length; i++) {
                const el = allNodes[i];
                const rect = el.getBoundingClientRect();
                const style = window.getComputedStyle(el);
                
                if (rect.width > 0 && rect.height > 0 && 
                    style.visibility !== 'hidden' && 
                    style.display !== 'none') {
                    
                    elements.push({
                        tagName: el.tagName,
                        className: el.className,
                        id: el.id,
                        textContent: (el.textContent || '').trim().substring(0, 100),
                        top: rect.top,
                        left: rect.left,
                        width: rect.width,
                        height: rect.height,
                        zIndex: style.zIndex,
                        position: style.position,
                        backgroundColor: style.backgroundColor,
                        opacity: style.opacity
                    });
                }
            }
            
            return elements;
        });

        // Filter for potential popup/modal elements
        const potentialPopups = allElements.filter(el => 
            (el.position === 'fixed' || el.position === 'absolute') &&
            (el.zIndex !== 'auto' && parseInt(el.zIndex) > 100) ||
            el.className.toLowerCase().includes('modal') ||
            el.className.toLowerCase().includes('popup') ||
            el.className.toLowerCase().includes('dialog') ||
            el.className.toLowerCase().includes('overlay')
        );

        console.log(`📊 Found ${allElements.length} visible elements`);
        console.log(`🎯 Found ${potentialPopups.length} potential popup elements`);

        // Log potential popups
        potentialPopups.forEach((popup, index) => {
            console.log(`🔍 Popup ${index + 1}:`);
            console.log(`   Tag: ${popup.tagName}`);
            console.log(`   Class: ${popup.className}`);
            console.log(`   ID: ${popup.id}`);
            console.log(`   Position: ${popup.position}`);
            console.log(`   Z-Index: ${popup.zIndex}`);
            console.log(`   Size: ${popup.width}x${popup.height}`);
            console.log(`   Location: (${popup.top}, ${popup.left})`);
            console.log(`   Text: ${popup.textContent.substring(0, 50)}...`);
            console.log('');
        });

        // Look specifically for "Login anyway" button
        const loginAnywayElements = allElements.filter(el => 
            el.textContent.toLowerCase().includes('login anyway')
        );

        console.log(`🎯 Found ${loginAnywayElements.length} "Login anyway" elements:`);
        loginAnywayElements.forEach((el, index) => {
            console.log(`🔐 Login Anyway ${index + 1}:`);
            console.log(`   Tag: ${el.tagName}`);
            console.log(`   Class: ${el.className}`);
            console.log(`   Position: (${el.top}, ${el.left})`);
            console.log(`   Size: ${el.width}x${el.height}`);
            console.log(`   Z-Index: ${el.zIndex}`);
            console.log(`   Text: ${el.textContent}`);
            console.log('');
        });

        // Look for elements that might be blocking the login button
        if (loginAnywayElements.length > 0) {
            const loginButton = loginAnywayElements[0];
            const blockingElements = allElements.filter(el => 
                el.top <= loginButton.top &&
                el.left <= loginButton.left &&
                (el.top + el.height) >= (loginButton.top + loginButton.height) &&
                (el.left + el.width) >= (loginButton.left + loginButton.width) &&
                parseInt(el.zIndex || 0) > parseInt(loginButton.zIndex || 0)
            );

            console.log(`🚫 Found ${blockingElements.length} elements potentially blocking the login button:`);
            blockingElements.forEach((el, index) => {
                console.log(`🚫 Blocking Element ${index + 1}:`);
                console.log(`   Tag: ${el.tagName}`);
                console.log(`   Class: ${el.className}`);
                console.log(`   ID: ${el.id}`);
                console.log(`   Position: (${el.top}, ${el.left})`);
                console.log(`   Size: ${el.width}x${el.height}`);
                console.log(`   Z-Index: ${el.zIndex}`);
                console.log(`   Text: ${el.textContent.substring(0, 100)}...`);
                console.log('');
            });
        }

        // Try to find close buttons more aggressively
        const potentialCloseButtons = allElements.filter(el => 
            (el.textContent === '×' || el.textContent === '✕' || el.textContent === 'X') ||
            el.className.toLowerCase().includes('close') ||
            el.id.toLowerCase().includes('close') ||
            (el.width < 50 && el.height < 50 && el.top < 400) // Small elements in upper area
        );

        console.log(`🚫 Found ${potentialCloseButtons.length} potential close buttons:`);
        potentialCloseButtons.forEach((el, index) => {
            console.log(`🚫 Close Button ${index + 1}:`);
            console.log(`   Tag: ${el.tagName}`);
            console.log(`   Class: ${el.className}`);
            console.log(`   ID: ${el.id}`);
            console.log(`   Position: (${el.top}, ${el.left})`);
            console.log(`   Size: ${el.width}x${el.height}`);
            console.log(`   Text: "${el.textContent}"`);
            console.log('');
        });

        // Save detailed analysis to file
        const analysis = {
            totalElements: allElements.length,
            potentialPopups: potentialPopups,
            loginAnywayElements: loginAnywayElements,
            potentialCloseButtons: potentialCloseButtons,
            timestamp: new Date().toISOString()
        };

        fs.writeFileSync('popup-analysis.json', JSON.stringify(analysis, null, 2));
        console.log('✅ Detailed analysis saved to popup-analysis.json');

        // Try clicking the most likely close button
        if (potentialCloseButtons.length > 0) {
            console.log('🎯 Attempting to click the most likely close button...');
            
            // Sort by position (top-right corner preference) and size (smaller is more likely to be close button)
            const sortedCloseButtons = potentialCloseButtons.sort((a, b) => {
                const aScore = (1000 - a.top) + (a.left) + (50 - a.width) + (50 - a.height);
                const bScore = (1000 - b.top) + (b.left) + (50 - b.width) + (50 - b.height);
                return bScore - aScore;
            });

            const bestCloseButton = sortedCloseButtons[0];
            console.log(`🎯 Best close button candidate: ${bestCloseButton.tagName} at (${bestCloseButton.top}, ${bestCloseButton.left})`);

            try {
                // Click at the coordinates of the close button
                await page.click(`${bestCloseButton.tagName}`, { 
                    position: { 
                        x: bestCloseButton.width / 2, 
                        y: bestCloseButton.height / 2 
                    } 
                });
                console.log('✅ Clicked close button');
                
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // Take screenshot after clicking
                await page.screenshot({ path: 'debug-after-close-click.png', fullPage: true });
                console.log('📸 After close click screenshot saved');
                
            } catch (error) {
                console.log(`⚠️ Failed to click close button: ${error.message}`);
            }
        }

    } catch (error) {
        console.error('❌ Debug error:', error.message);
        
        await page.screenshot({ path: 'debug-error.png', fullPage: true });
        console.log('📸 Error screenshot saved');
        
    } finally {
        await browser.close();
        console.log('✅ Browser closed');
    }
}

// Run the debug
if (require.main === module) {
    debugBlockingPopup()
        .then(() => {
            console.log('\n🎉 Debug analysis complete!');
            console.log('Check popup-analysis.json for detailed information.');
        })
        .catch(error => {
            console.error('\n❌ Debug failed:', error.message);
            process.exit(1);
        });
}

module.exports = { debugBlockingPopup };
