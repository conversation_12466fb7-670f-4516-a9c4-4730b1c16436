{"version": 3, "file": "XPathQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/XPathQuerySelector.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,EAC5C,IAAU,EACV,QAAgB,EAChB,UAAU,GAAG,CAAC,CAAC;IAEf,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC;IAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAC3B,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,WAAW,CAAC,0BAA0B,CACvC,CAAC;IACF,MAAM,KAAK,GAAG,EAAE,CAAC;IACjB,IAAI,IAAI,CAAC;IAET,oCAAoC;IACpC,mHAAmH;IACnH,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;QACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,IAAI,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAC9C,MAAM;QACR,CAAC;IACH,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAChB,MAAM,IAAY,CAAC;QACnB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC"}