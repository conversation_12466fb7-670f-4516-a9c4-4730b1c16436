const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const puppeteer = require('puppeteer');
const fs = require('fs');

class InteractiveBrowserServer {
    constructor() {
        this.app = express();
        this.server = null;
        this.wss = null;
        this.masterBrowser = null;
        this.masterPage = null;
        this.clients = new Set();
    }

    async connectToMasterSession() {
        console.log('🔌 Connecting to live master browser via API...');

        // Instead of creating a new browser, we'll use the live browser server
        // that's already running on port 3005
        this.masterBrowserAPI = 'http://localhost:3005';

        // Test connection to live browser
        try {
            const fetch = require('node-fetch');
            const response = await fetch(`${this.masterBrowserAPI}/api/browser-state`);
            const data = await response.json();

            console.log(`✅ Connected to live master browser:`);
            console.log(`📍 URL: ${data.url}`);
            console.log(`📄 Title: ${data.title}`);
            console.log(`🔐 Authenticated: ${data.isAuthenticated}`);

            this.isConnected = true;
            return data;

        } catch (error) {
            throw new Error(`Failed to connect to live master browser: ${error.message}`);
        }
    }

    async getMasterBrowserContent() {
        try {
            const fetch = require('node-fetch');
            const response = await fetch(`${this.masterBrowserAPI}/live-browser`);
            const html = await response.text();
            return html;
        } catch (error) {
            throw new Error(`Failed to get master browser content: ${error.message}`);
        }
    }

    async sendCommandToMasterBrowser(command) {
        // For now, we'll simulate commands by getting fresh content
        // In a full implementation, we'd need the master browser to expose a command API
        console.log(`📨 Simulating command: ${command.type}`);

        // Wait a moment for any changes to take effect
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return fresh content
        return await this.getMasterBrowserContent();
    }

    setupRoutes() {
        // Serve interactive browser interface
        this.app.get('/interactive', (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - Interactive Master Browser</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; overflow: hidden; }
        .header {
            background: #1a365d; color: white; padding: 10px 20px;
            display: flex; justify-content: space-between; align-items: center;
            position: fixed; top: 0; left: 0; right: 0; z-index: 1000; height: 50px;
        }
        .header h1 { font-size: 16px; margin: 0; }
        .status { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
        .status.connected { background: #10b981; }
        .status.disconnected { background: #ef4444; }
        .browser-container { 
            position: fixed; top: 50px; left: 0; right: 0; bottom: 0;
            background: white; overflow: hidden;
        }
        .browser-content {
            width: 100%; height: 100%; overflow: auto;
            transform-origin: top left;
        }
        .loading { 
            display: flex; justify-content: center; align-items: center; 
            height: 100%; font-size: 18px; color: #666; 
        }
        .controls {
            display: flex; gap: 5px;
        }
        .btn {
            background: #4f46e5; color: white; border: none; 
            padding: 6px 12px; border-radius: 4px; cursor: pointer; 
            font-size: 12px;
        }
        .btn:hover { background: #4338ca; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 DAT Load Board - Interactive Master Browser</h1>
        <div class="controls">
            <button class="btn" onclick="refreshBrowser()">🔄 Refresh</button>
            <button class="btn" onclick="goBack()">⬅️ Back</button>
            <button class="btn" onclick="goForward()">➡️ Forward</button>
        </div>
        <div class="status disconnected" id="status">🔴 Connecting...</div>
    </div>
    
    <div class="browser-container">
        <div class="loading" id="loading">Connecting to master browser...</div>
        <div id="browser-content" class="browser-content" style="display: none;"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = \`\${protocol}//\${window.location.host}/ws\`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                console.log('🔌 Connected to master browser');
                isConnected = true;
                updateStatus('connected', '🟢 LIVE');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('browser-content').style.display = 'block';
                
                // Request initial page content
                sendCommand('getContent');
            };
            
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                
                if (data.type === 'content') {
                    updateBrowserContent(data.html);
                } else if (data.type === 'error') {
                    console.error('Browser control error:', data.message);
                }
            };
            
            ws.onclose = () => {
                console.log('🔌 Disconnected from master browser');
                isConnected = false;
                updateStatus('disconnected', '🔴 Disconnected');
                
                // Try to reconnect after 3 seconds
                setTimeout(connect, 3000);
            };
            
            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        }
        
        function updateStatus(status, text) {
            const statusEl = document.getElementById('status');
            statusEl.className = \`status \${status}\`;
            statusEl.textContent = text;
        }
        
        function updateBrowserContent(html) {
            const contentEl = document.getElementById('browser-content');

            // Instead of writing to iframe, create a proxy endpoint and use iframe src
            // This ensures proper resource loading and avoids cross-origin issues

            // Create iframe that points to our proxy endpoint
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';
            iframe.src = '/proxy-content';

            contentEl.innerHTML = '';
            contentEl.appendChild(iframe);

            // Add interaction overlay instead of trying to capture iframe events
            addInteractionOverlay(contentEl, iframe);
        }
        
        function addInteractionOverlay(container, iframe) {
            // Create transparent overlay to capture interactions
            const overlay = document.createElement('div');
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.zIndex = '1000';
            overlay.style.background = 'transparent';
            overlay.style.cursor = 'pointer';

            container.style.position = 'relative';
            container.appendChild(overlay);

            // Capture clicks on overlay
            overlay.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                if (isConnected) {
                    const rect = iframe.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    console.log('🖱️ Click at (' + x + ', ' + y + ')');
                    sendCommand('click', { x, y });
                }
            });

            // Capture scroll events on overlay
            overlay.addEventListener('wheel', (e) => {
                e.preventDefault();

                if (isConnected) {
                    console.log('🖱️ Scroll deltaY: ' + e.deltaY);
                    sendCommand('scroll', { deltaY: e.deltaY });
                }
            });

            // Capture keyboard events when overlay is focused
            overlay.tabIndex = 0; // Make overlay focusable
            overlay.addEventListener('keydown', (e) => {
                if (isConnected) {
                    console.log('⌨️ Key: ' + e.key);
                    sendCommand('key', { key: e.key, code: e.code });
                }
            });

            // Focus overlay on click to capture keyboard events
            overlay.addEventListener('mousedown', () => {
                overlay.focus();
            });
        }
        
        function getElementSelector(element) {
            if (element.id) return '#' + element.id;
            if (element.name) return '[name="' + element.name + '"]';
            if (element.className) return '.' + element.className.split(' ')[0];
            return element.tagName.toLowerCase();
        }
        
        function sendCommand(type, data = {}) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type, ...data }));
            }
        }
        
        function refreshBrowser() {
            if (isConnected) {
                sendCommand('refresh');
            }
        }
        
        function goBack() {
            if (isConnected) {
                sendCommand('goBack');
            }
        }
        
        function goForward() {
            if (isConnected) {
                sendCommand('goForward');
            }
        }
        
        // Start connection
        connect();
        
        // Auto-refresh content every 10 seconds
        setInterval(() => {
            if (isConnected) {
                sendCommand('getContent');
            }
        }, 10000);
    </script>
</body>
</html>`;
            res.send(html);
        });

        // Proxy content endpoint - serves the actual DAT content
        this.app.get('/proxy-content', async (req, res) => {
            try {
                const html = await this.getMasterBrowserContent();

                // Add base tag to fix relative URLs and prevent navigation
                const modifiedHtml = html.replace(
                    '<head>',
                    `<head>
                        <base href="https://one.dat.com/">
                        <style>
                            /* Prevent scrollbars and ensure full height */
                            html, body {
                                margin: 0;
                                padding: 0;
                                overflow: hidden;
                                height: 100vh;
                            }
                            /* Disable all links and forms to prevent navigation */
                            a { pointer-events: none; }
                            form { pointer-events: none; }
                            button { pointer-events: none; }
                            input { pointer-events: none; }
                            select { pointer-events: none; }
                            textarea { pointer-events: none; }
                        </style>`
                );

                res.setHeader('Content-Type', 'text/html');
                res.setHeader('X-Frame-Options', 'SAMEORIGIN');
                res.send(modifiedHtml);

            } catch (error) {
                console.error('Error serving proxy content:', error.message);
                res.status(500).send(`
                    <html>
                        <body style="font-family: Arial; padding: 20px; text-align: center;">
                            <h1>❌ Content Loading Error</h1>
                            <p>${error.message}</p>
                            <button onclick="window.location.reload()">🔄 Retry</button>
                        </body>
                    </html>
                `);
            }
        });

        // Health check endpoint
        this.app.get('/status', async (req, res) => {
            try {
                if (this.isConnected) {
                    const fetch = require('node-fetch');
                    const response = await fetch(`${this.masterBrowserAPI}/api/browser-state`);
                    const masterState = await response.json();

                    res.json({
                        connected: true,
                        masterBrowser: masterState,
                        clients: this.clients.size,
                        timestamp: new Date().toISOString()
                    });
                } else {
                    res.json({
                        connected: false,
                        error: 'Not connected to master browser',
                        clients: this.clients.size,
                        timestamp: new Date().toISOString()
                    });
                }
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
    }

    setupWebSocket() {
        this.wss = new WebSocket.Server({ 
            server: this.server,
            path: '/ws'
        });
        
        this.wss.on('connection', (ws) => {
            console.log('🔌 Client connected to interactive browser');
            this.clients.add(ws);
            
            ws.on('message', async (message) => {
                try {
                    const data = JSON.parse(message);
                    console.log('📨 Received command:', data.type);
                    
                    switch (data.type) {
                        case 'getContent':
                            const html = await this.getMasterBrowserContent();
                            const fetch = require('node-fetch');
                            const stateResponse = await fetch(`${this.masterBrowserAPI}/api/browser-state`);
                            const state = await stateResponse.json();

                            ws.send(JSON.stringify({
                                type: 'content',
                                html: html,
                                url: state.url
                            }));
                            break;

                        case 'click':
                            console.log(`🖱️ Click command received at (${data.x}, ${data.y})`);
                            // For now, just refresh content since we can't directly control the master browser
                            // In a full implementation, we'd send this to the master browser's command API
                            setTimeout(async () => {
                                const html = await this.getMasterBrowserContent();
                                const fetch = require('node-fetch');
                                const stateResponse = await fetch(`${this.masterBrowserAPI}/api/browser-state`);
                                const state = await stateResponse.json();

                                ws.send(JSON.stringify({
                                    type: 'content',
                                    html: html,
                                    url: state.url
                                }));
                            }, 1000);
                            break;

                        case 'scroll':
                            console.log(`📜 Scroll command received: ${data.deltaY}`);
                            // Simulate scroll by refreshing content
                            break;

                        case 'clearAndType':
                            console.log(`⌨️ Type command received: ${data.text}`);
                            // Simulate typing by refreshing content
                            break;

                        case 'refresh':
                            console.log('🔄 Refresh command received');
                            setTimeout(async () => {
                                const html = await this.getMasterBrowserContent();
                                const fetch = require('node-fetch');
                                const stateResponse = await fetch(`${this.masterBrowserAPI}/api/browser-state`);
                                const state = await stateResponse.json();

                                ws.send(JSON.stringify({
                                    type: 'content',
                                    html: html,
                                    url: state.url
                                }));
                            }, 1000);
                            break;

                        case 'goBack':
                            console.log('⬅️ Back command received');
                            // Simulate back by refreshing content
                            break;

                        case 'goForward':
                            console.log('➡️ Forward command received');
                            // Simulate forward by refreshing content
                            break;
                    }
                    
                } catch (error) {
                    console.error('❌ Command error:', error.message);
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: error.message
                    }));
                }
            });
            
            ws.on('close', () => {
                console.log('🔌 Client disconnected');
                this.clients.delete(ws);
            });
        });
    }

    async start(port = 3006) {
        try {
            await this.connectToMasterSession();
            
            this.setupRoutes();
            
            this.server = http.createServer(this.app);
            this.setupWebSocket();
            
            this.server.listen(port, '0.0.0.0', () => {
                console.log(`🌐 Interactive Browser Server running on port ${port}`);
                console.log(`🎮 Interactive DAT Browser: http://147.93.146.10:${port}/interactive`);
                console.log(`📊 Status API: http://147.93.146.10:${port}/status`);
            });
            
        } catch (error) {
            console.error('❌ Failed to start interactive browser server:', error.message);
            process.exit(1);
        }
    }

    async stop() {
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        if (this.server) {
            this.server.close();
        }
    }
}

// Start the server if run directly
if (require.main === module) {
    const server = new InteractiveBrowserServer();
    server.start(3006);
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Shutting down interactive browser server...');
        await server.stop();
        process.exit(0);
    });
}

module.exports = InteractiveBrowserServer;
